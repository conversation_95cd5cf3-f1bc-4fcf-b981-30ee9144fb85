import { faker } from '@faker-js/faker';

import { factory, models } from './index.js';

import { HIERARCHY } from '#src/modules/core/constants/core.constant.js';

const { Entity } = models;

/**
 * Select a random organisation entity.
 * NOTE: ORDER BY RANDOM() is fine for factories/dev data.
 * @returns {Promise<Entity|null>}
 */
const findRandomOrganisation = () =>
  Entity.findOne({
    where: { hierarchy: HIERARCHY.ORGANISATION },
    order: Entity.sequelize.random(),
  });

/**
 * Set parentId (and possibly hierarchy) for an entity based on its current hierarchy.
 *
 * Rules:
 * - organisation → parentId = root.id
 * - merchant     → parentId = random organisation.id
 *     - if none exists → parentId = root.id and hierarchy → organisation
 *
 * (Behaviour preserved exactly as original: queries root first and uses it.)
 *
 * @param {Entity} entity
 * @returns {Promise<Entity>}
 */
const setParent = async (entity) => {
  const root = await Entity.findOne({ where: { hierarchy: HIERARCHY.ROOT } });

  switch (entity.hierarchy) {
    case HIERARCHY.ORGANISATION: {
      entity.parentId = root.id;
      break;
    }
    case HIERARCHY.MERCHANT: {
      const org = await findRandomOrganisation();
      if (!org) {
        entity.parentId = root.id;
        entity.hierarchy = HIERARCHY.ORGANISATION;
      } else {
        entity.parentId = org.id;
      }
      break;
    }
    default:
      // No change for other/unknown hierarchies (none in current usage).
      break;
  }

  return entity;
};

factory.define(
  'entity',
  Entity,
  {
    hierarchy: async () => {
      const hierarchies = [HIERARCHY.ORGANISATION, HIERARCHY.MERCHANT];
      return faker.helpers.arrayElement(hierarchies);
    },
    accessId: () => faker.string.numeric(12),
    code: () =>
      faker.string.alpha({
        length: { min: 3, max: 10 },
        casing: 'upper',
      }),
    prefix: () =>
      faker.string.alpha({
        length: { min: 3, max: 10 },
        casing: 'upper',
      }),
    name: () => faker.company.name(),
    description: () => faker.lorem.sentences(),
    phone: () => faker.helpers.replaceSymbols('601########'),
    email: () => faker.internet.email(),
  },
  {
    afterBuild: async (entity) => setParent(entity),
  },
);
