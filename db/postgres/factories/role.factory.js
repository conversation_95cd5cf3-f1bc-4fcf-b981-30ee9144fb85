import { faker } from '@faker-js/faker';
import { Op } from 'sequelize';

import { factory, models } from './index.js';

import { POLICIES } from '#src/modules/user/constants/index.js';

const { Role, Department, Entity, Module, Policy, RoleModule } = models;
const NAME_MAX = 50;
const pad = (n) => String(n).padStart(4, '0');

/**
 * Copy selected policy fields from a Module-level Policy
 * to a RoleModule-level Policy payload.
 */
const toRolePolicyDefaults = (modulePolicy, audit = {}) => {
  // If module has a policy, clone it; otherwise default all to false.
  const base = modulePolicy || {};
  const bool = (v) => v === true; // ensure booleans

  return {
    canView: bool(base.canView),
    canCreate: bool(base.canCreate),
    canEdit: bool(base.canEdit),
    canImport: bool(base.canImport),
    canExport: bool(base.canExport),
    canManage: bool(base.canManage),
    canMasking: bool(base.canMasking),
    canOverwrite: bool(base.canOverwrite),
    canVerify: bool(base.canVerify),
  };
};

/**
 * Ensure role has an entity
 * - If no entityId is provided, create a new Entity
 *
 * @param {Role} role
 * @returns {Promise<Role>}
 */
const withEntity = async (role) => {
  if (!role.entityId) {
    const entity = await factory.create('entity');
    role.entityId = entity.id;
  }
  return role;
};

/**
 * Ensure role has a department
 * - If no departmentId is provided, create a new Department under the same entity
 *
 * @param {Role} role
 * @param {Object} opts
 * @returns {Promise<Role>}
 */
const withDepartment = async (role, opts = {}) => {
  const { transaction: t } = opts;

  if (!role.departmentId) {
    // Create a department in the same entity
    const department = await factory.create(
      'department',
      {
        entityId: role.entityId,
      },
      { transaction: t },
    );
    role.departmentId = department.id;
  }

  return role;
};

/**
 * Generate role path based on parent and name
 * - If parentId is null, this is a root role
 * - If parentId exists, append to parent's path
 *
 * @param {Role} role
 * @param {Object} opts
 * @returns {Promise<Role>}
 */
const withPath = async (role, opts = {}) => {
  const { transaction: t } = opts;

  let parentPath = null;
  if (role.parentId) {
    const parentRole = await Role.findByPk(role.parentId, { transaction: t });
    if (parentRole) {
      parentPath = parentRole.path;
    }
  }

  const safeLabel = role.name
    .trim()
    .toLowerCase()
    .replace(/[^a-z0-9_]/g, '_')
    .replace(/_+/g, '_');

  // If no parent, this is a root role
  role.path = parentPath ? `${parentPath}.${safeLabel}` : safeLabel;
  return role;
};

/**
 * Ensure every RoleModule has a Policy.
 * - Get entity hierarchy to find matching modules
 * - Load modules (same hierarchy, navigation_url not null) incl. their policy.
 * - For child roles, validate against parent role's module access using same logic as role.validation.js
 * - Link to role via RoleModule.
 * - Bulk insert Policy for ALL RoleModules (skip dupes).
 */
const withModules = async (role, opts = {}) => {
  const { transaction: t, createdBy = null, updatedBy = null } = opts;

  // STEP 1: Get the entity to determine hierarchy
  const entity = await Entity.findByPk(role.entityId, { transaction: t });
  if (!entity) {
    return; // No entity, can't determine hierarchy
  }

  // STEP 2: Find modules in the same hierarchy with non-null navigation_url
  const availableModules = await Module.findAll({
    where: {
      hierarchy: entity.hierarchy,
      navigation_url: {
        [Op.ne]: null, // not equal to null
      },
    },
    include: [
      {
        model: Policy,
        as: 'policy',
        required: false, // a module may not have a policy yet
      },
    ],
    transaction: t,
  });

  if (availableModules.length === 0) {
    return; // nothing to do
  }

  // STEP 3: Get parent policies if this is a child role (similar to validation logic)
  let parentPoliciesMap = new Map();
  if (role.parentId) {
    const parentRoleModules = await RoleModule.findAll({
      where: { roleId: role.parentId },
      include: [
        {
          model: Policy,
          as: 'policy',
          required: false,
        },
      ],
      transaction: t,
    });

    // Process parent policies using similar logic to processPolicyRows
    for (const rm of parentRoleModules) {
      if (rm.policy) {
        const policyData = rm.policy.toJSON();
        const enabledPolicies = new Set();

        for (const [key, value] of Object.entries(policyData)) {
          if (POLICIES.includes(key) && value === true) {
            enabledPolicies.add(key);
          }
        }

        if (enabledPolicies.size > 0) {
          parentPoliciesMap.set(rm.moduleId, enabledPolicies);
        }
      }
    }
  }

  // STEP 4: Filter modules based on parent access (if child role)
  const accessibleModules = role.parentId
    ? availableModules.filter((module) => parentPoliciesMap.has(module.id))
    : availableModules;

  if (accessibleModules.length === 0) {
    return; // No accessible modules
  }

  // STEP 5: Create RoleModule records
  const roleModuleData = accessibleModules.map((module) => ({
    roleId: role.id,
    moduleId: module.id,
    createdBy,
    updatedBy,
  }));

  await RoleModule.bulkCreate(roleModuleData, {
    transaction: t,
    ignoreDuplicates: true,
  });

  // STEP 6: Get all RoleModule records for this role and these modules
  const moduleIds = accessibleModules.map((m) => m.id);
  const allRoleModules = await RoleModule.findAll({
    where: { roleId: role.id, moduleId: moduleIds },
    transaction: t,
  });

  // Quick lookup: moduleId -> Module (with its policy)
  const modById = new Map(accessibleModules.map((m) => [m.id, m]));

  // STEP 7: Build payloads for ALL roleModules
  const policyRows = [];
  for (const rm of allRoleModules) {
    const mod = modById.get(rm.moduleId);
    let defaults = toRolePolicyDefaults(mod?.policy);

    // If this is a child role, restrict policies to parent's enabled policies
    if (role.parentId && parentPoliciesMap.has(rm.moduleId)) {
      const parentEnabledPolicies = parentPoliciesMap.get(rm.moduleId);

      // Only enable policies that are both in defaults AND in parent's enabled policies
      defaults = {
        canView: defaults.canView && parentEnabledPolicies.has('canView'),
        canCreate: defaults.canCreate && parentEnabledPolicies.has('canCreate'),
        canEdit: defaults.canEdit && parentEnabledPolicies.has('canEdit'),
        canImport: defaults.canImport && parentEnabledPolicies.has('canImport'),
        canExport: defaults.canExport && parentEnabledPolicies.has('canExport'),
        canManage: defaults.canManage && parentEnabledPolicies.has('canManage'),
        canMasking: defaults.canMasking && parentEnabledPolicies.has('canMasking'),
        canOverwrite: defaults.canOverwrite && parentEnabledPolicies.has('canOverwrite'),
        canVerify: defaults.canVerify && parentEnabledPolicies.has('canVerify'),
      };
    }

    policyRows.push({
      parentId: rm.id, // RoleModule.hasOne(Policy) via parentId
      ...defaults,
      createdBy,
      updatedBy,
    });
  }

  // STEP 8: Bulk insert for all; rely on UNIQUE(parentId) to skip existing
  await Policy.bulkCreate(policyRows, {
    transaction: t,
    ignoreDuplicates: true, // requires UNIQUE on parentId
  });

  return role;
};

/**
 * Role Factory
 *
 * Generates a Role record for development/testing purposes.
 * - Automatically creates a new Entity if no entityId is passed.
 * - Automatically creates a new Department if no departmentId is passed.
 * - Associates with modules based on the Entity's hierarchy.
 * - Generates proper role path based on parent hierarchy.
 * - If parentId is null, creates a root/highest level role.
 *
 * CLI Usage Examples:
 *   1. Create 1 root role with new Entity and Department:
 *        npm run factory -- role
 *
 *   2. Create 3 root roles with new Entities and Departments:
 *        npm run factory -- role --count=3
 *
 *   3. Create 2 roles under an existing Entity and Department:
 *        npm run factory -- role --count=2 --entityId=<existingEntityId> --departmentId=<existingDepartmentId>
 *
 *   4. Create a child role under an existing parent role:
 *        npm run factory -- role --parentId=<existingRoleId> --entityId=<existingEntityId> --departmentId=<existingDepartmentId>
 *
 *   5. Build (but do not save) 3 roles:
 *        npm run factory -- role --count=3 --build
 */
factory.define(
  'role',
  Role,
  {
    name: factory.sequence((n) => {
      const base = faker.person.jobTitle();
      const suffix = `-R${pad(n)}`;
      return `${base.slice(0, NAME_MAX - suffix.length)}${suffix}`;
    }),
    description: () => faker.lorem.sentence({ min: 5, max: 10 }),
    status: 'active',
    parentId: null, // Default to root role (highest level)
  },
  {
    afterBuild: async (role, opts) => {
      await withEntity(role);
      await withDepartment(role, opts);
      await withPath(role, opts);
      return role;
    },
    afterCreate: withModules,
  },
);
