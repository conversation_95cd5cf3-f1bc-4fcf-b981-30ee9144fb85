import { beforeEach, describe, expect, it, vi } from 'vitest';

import { CoreError } from '#src/modules/core/errors/index.js';
import { ModuleRepository } from '#src/modules/core/repository/index.js';
import {
  validateAndFilterModulePolicies,
  validateHierarchy,
  validateParentPolicies,
  validatePoliciesForHierarchy,
  validateRoleName,
  validateSupportedPolicies,
} from '#src/modules/core/validations/role.validation.js';
import { RoleError } from '#src/modules/user/errors/index.js';
import {
  DepartmentRepository,
  PolicyRepository,
  RoleModuleRepository,
  RoleRepository,
} from '#src/modules/user/repository/index.js';

vi.mock('#src/modules/user/repository/index.js', () => ({
  DepartmentRepository: {
    findAllModulePolicies: vi.fn(),
  },
  PolicyRepository: {
    findByParentId: vi.fn(),
    findByParentIds: vi.fn(),
  },
  RoleModuleRepository: {
    findAllByRoleIdWithPolicy: vi.fn(),
  },
  RoleRepository: {
    findByEntityIdAndName: vi.fn(),
  },
}));

vi.mock('#src/modules/core/repository/index.js', () => ({
  ModuleRepository: {
    findByIds: vi.fn(),
  },
}));

vi.mock('#src/modules/user/errors/index.js', () => ({
  RoleError: {
    unsupportedModulePolicies: vi.fn().mockImplementation(() => {
      throw new Error('Unsupported policies');
    }),
    exceedsParentPolicies: vi.fn().mockImplementation(() => {
      throw new Error('Exceeds parent policies');
    }),
    parentHasNoPoliciesAccess: vi.fn().mockImplementation(() => {
      throw new Error('Parent has no policies access');
    }),
    invalidHierarchy: vi.fn().mockImplementation(() => {
      throw new Error('Invalid hierarchy');
    }),
    moduleNotInDepartment: vi.fn().mockImplementation(() => {
      throw new Error('Module not in department');
    }),
  },
}));

vi.mock('#src/modules/core/errors/index.js', () => ({
  CoreError: {
    dataNotFound: vi.fn().mockImplementation(() => {
      throw new Error('Data not found');
    }),
    alreadyExists: vi.fn().mockImplementation(() => {
      throw new Error('Role name exists');
    }),
  },
}));

describe('Role Validation', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('validateRoleName', () => {
    it('should not throw error when role name is unique', async () => {
      RoleRepository.findByEntityIdAndName.mockResolvedValue(null);

      await expect(validateRoleName('server', 'entity-1', 'Admin')).resolves.not.toThrow();
      expect(RoleRepository.findByEntityIdAndName).toHaveBeenCalledWith(
        'server',
        'entity-1',
        'Admin',
        null,
      );
    });

    it('should throw error when role name is not unique', async () => {
      RoleRepository.findByEntityIdAndName.mockResolvedValue({ id: 'role-1', name: 'Admin' });

      await expect(validateRoleName('server', 'entity-1', 'Admin')).rejects.toThrow();
      expect(CoreError.alreadyExists).toHaveBeenCalled();
    });

    it('should exclude the specified role ID when checking uniqueness', async () => {
      RoleRepository.findByEntityIdAndName.mockResolvedValue(null);

      await validateRoleName('server', 'entity-1', 'Admin', 'role-1');
      expect(RoleRepository.findByEntityIdAndName).toHaveBeenCalledWith(
        'server',
        'entity-1',
        'Admin',
        'role-1',
      );
    });
  });

  describe('validateAndFilterModulePolicies', () => {
    const mockServer = 'server';
    const mockModules = [
      { moduleId: 'module-1', policies: ['canView', 'canEdit'] },
      { moduleId: 'module-2', policies: ['canView'] },
      { moduleId: 'module-1', policies: ['canView'] },
    ];
    const mockHierarchy = 'organisation';

    beforeEach(() => {
      vi.clearAllMocks();

      ModuleRepository.findByIds.mockResolvedValue([
        { id: 'module-1', hierarchy: 'organisation', translationKey: 'module.one' },
        { id: 'module-2', hierarchy: 'merchant', translationKey: 'module.two' },
      ]);

      PolicyRepository.findByParentIds.mockResolvedValue([
        {
          toJSON: () => ({
            parentId: 'module-1',
            canView: true,
            canEdit: true,
            canCreate: false,
          }),
        },
        {
          toJSON: () => ({
            parentId: 'module-2',
            canView: true,
            canEdit: false,
          }),
        },
      ]);

      RoleModuleRepository.findAllByRoleIdWithPolicy.mockResolvedValue([
        {
          moduleId: 'module-1',
          policies: {
            toJSON: () => ({ canView: true, canEdit: true }),
          },
        },
        {
          moduleId: 'module-2',
          policies: {
            toJSON: () => ({ canView: true }),
          },
        },
      ]);
    });

    it('should deduplicate modules and validate policies', async () => {
      const result = await validateAndFilterModulePolicies(mockServer, mockModules, mockHierarchy);

      expect(result).toHaveLength(2);

      expect(ModuleRepository.findByIds).toHaveBeenCalledWith(mockServer, ['module-1', 'module-2']);
      expect(PolicyRepository.findByParentIds).toHaveBeenCalledWith(mockServer, [
        'module-1',
        'module-2',
      ]);

      expect(result).toEqual([
        { moduleId: 'module-1', policies: ['canView', 'canEdit'] },
        { moduleId: 'module-2', policies: ['canView'] },
      ]);
    });

    it('should throw error when modules are not in department', async () => {
      const mockServer = 'server';
      const mockModules = [
        { moduleId: 'module-1', policies: ['canView'] },
        { moduleId: 'module-2', policies: ['canEdit'] },
      ];
      const mockHierarchy = 'organisation';
      const departmentId = 'dept-1';

      ModuleRepository.findByIds.mockResolvedValue([
        { id: 'module-1', hierarchy: 'organisation', translationKey: 'module.one' },
        { id: 'module-2', hierarchy: 'organisation', translationKey: 'module.two' },
      ]);

      PolicyRepository.findByParentIds.mockResolvedValue([
        {
          toJSON: () => ({
            parentId: 'module-1',
            canView: true,
          }),
        },
        {
          toJSON: () => ({
            parentId: 'module-2',
            canEdit: true,
          }),
        },
      ]);

      // Mock department modules to only include module-1, excluding module-2
      DepartmentRepository.findAllModulePolicies.mockResolvedValue([{ moduleId: 'module-1' }]);

      await expect(
        validateAndFilterModulePolicies(mockServer, mockModules, mockHierarchy, null, departmentId),
      ).rejects.toThrow();

      expect(RoleError.moduleNotInDepartment).toHaveBeenCalledWith({
        moduleId: 'module-2',
        departmentId: 'dept-1',
      });
    });

    it('should validate against parent role policies', async () => {
      const parentId = 'parent-role-1';
      const modulesExceedingParent = [
        { moduleId: 'module-1', policies: ['canView', 'canEdit'] },
        { moduleId: 'module-2', policies: ['canView', 'canEdit'] },
      ];

      RoleModuleRepository.findAllByRoleIdWithPolicy.mockResolvedValue([
        {
          moduleId: 'module-1',
          policy: { toJSON: () => ({ canView: true }) },
        },
        {
          moduleId: 'module-2',
          policy: { toJSON: () => ({ canView: true }) },
        },
      ]);

      await expect(
        validateAndFilterModulePolicies(
          mockServer,
          modulesExceedingParent,
          mockHierarchy,
          parentId,
        ),
      ).rejects.toThrow('Exceeds parent policies');
    });

    it('should throw error if module is not in parent role', async () => {
      const parentId = 'parent-role-1';

      RoleModuleRepository.findAllByRoleIdWithPolicy.mockResolvedValue([
        {
          moduleId: 'module-1',
          policy: {
            toJSON: () => ({ canView: true, canEdit: true }),
          },
        },
      ]);

      const modulesNotInParent = [{ moduleId: 'module-2', policies: ['canView'] }];

      await expect(
        validateAndFilterModulePolicies(mockServer, modulesNotInParent, mockHierarchy, parentId),
      ).rejects.toThrow('Parent has no policies access');

      expect(RoleError.parentHasNoPoliciesAccess).toHaveBeenCalledWith({
        module: 'module.two',
      });
    });

    it('should throw error when module is not found', async () => {
      ModuleRepository.findByIds.mockResolvedValue([
        { id: 'module-1', hierarchy: 'organisation', translationKey: 'module.one' },
      ]);

      const modulesWithNonExistent = [
        { moduleId: 'module-1', policies: ['canView'] },
        { moduleId: 'non-existent-module', policies: ['canView'] },
      ];

      await expect(
        validateAndFilterModulePolicies(mockServer, modulesWithNonExistent, mockHierarchy),
      ).rejects.toThrow('Data not found');

      expect(CoreError.dataNotFound).toHaveBeenCalledWith({
        data: 'common.label.module',
        attribute: 'ID',
        value: 'non-existent-module',
      });
    });
  });

  describe('validatePoliciesForHierarchy', () => {
    it('should not throw error when module hierarchy is allowed for user hierarchy', () => {
      expect(() => validatePoliciesForHierarchy('root', 'root', 'module-1')).not.toThrow();
      expect(() => validatePoliciesForHierarchy('organisation', 'root', 'module-1')).not.toThrow();
      expect(() => validatePoliciesForHierarchy('merchant', 'root', 'module-1')).not.toThrow();
      expect(() =>
        validatePoliciesForHierarchy('organisation', 'organisation', 'module-1'),
      ).not.toThrow();
      expect(() =>
        validatePoliciesForHierarchy('merchant', 'organisation', 'module-1'),
      ).not.toThrow();
      expect(() => validatePoliciesForHierarchy('merchant', 'merchant', 'module-1')).not.toThrow();
    });

    it('should throw error when module hierarchy is not allowed for user hierarchy', () => {
      expect(() => validatePoliciesForHierarchy('root', 'organisation', 'module-1')).toThrow(
        'Invalid hierarchy',
      );
      expect(RoleError.invalidHierarchy).toHaveBeenCalledWith({ id: 'module-1' });

      vi.clearAllMocks();

      expect(() => validatePoliciesForHierarchy('root', 'merchant', 'module-2')).toThrow(
        'Invalid hierarchy',
      );
      expect(RoleError.invalidHierarchy).toHaveBeenCalledWith({ id: 'module-2' });

      vi.clearAllMocks();

      expect(() => validatePoliciesForHierarchy('organisation', 'merchant', 'module-3')).toThrow(
        'Invalid hierarchy',
      );
      expect(RoleError.invalidHierarchy).toHaveBeenCalledWith({ id: 'module-3' });
    });

    it('should handle case sensitivity in hierarchy names', () => {
      expect(() => validatePoliciesForHierarchy('ROOT', 'root', 'module-1')).toThrow(
        'Invalid hierarchy',
      );
      expect(() =>
        validatePoliciesForHierarchy('Organisation', 'organisation', 'module-2'),
      ).toThrow('Invalid hierarchy');
      expect(() => validatePoliciesForHierarchy('MERCHANT', 'merchant', 'module-3')).toThrow(
        'Invalid hierarchy',
      );
    });
  });

  describe('validateSupportedPolicies', () => {
    it('should not throw error when all policies are supported', () => {
      const moduleId = 'module-1';
      const requestedPolicies = ['canView', 'canEdit'];
      const supportedPoliciesMap = new Map([
        ['module-1', new Set(['canView', 'canEdit', 'canDelete'])],
      ]);
      const moduleInfo = { translationKey: 'module.one' };

      expect(() => {
        validateSupportedPolicies(moduleId, requestedPolicies, supportedPoliciesMap, moduleInfo);
      }).not.toThrow();
    });

    it('should throw error when some policies are not supported', () => {
      const moduleId = 'module-1';
      const requestedPolicies = ['canView', 'canEdit', 'canDelete'];
      const supportedPoliciesMap = new Map([['module-1', new Set(['canView', 'canEdit'])]]);
      const moduleInfo = { translationKey: 'module.one' };

      expect(() => {
        validateSupportedPolicies(moduleId, requestedPolicies, supportedPoliciesMap, moduleInfo);
      }).toThrow('Unsupported policies');

      expect(RoleError.unsupportedModulePolicies).toHaveBeenCalledWith({
        module: 'module.one',
        policies: ['canDelete'],
      });
    });

    it('should handle empty supported policies map', () => {
      const moduleId = 'module-1';
      const requestedPolicies = ['canView'];
      const supportedPoliciesMap = new Map();
      const moduleInfo = { translationKey: 'module.one' };

      expect(() => {
        validateSupportedPolicies(moduleId, requestedPolicies, supportedPoliciesMap, moduleInfo);
      }).toThrow('Unsupported policies');

      expect(RoleError.unsupportedModulePolicies).toHaveBeenCalledWith({
        module: 'module.one',
        policies: ['canView'],
      });
    });

    it('should not throw error when no policies to validate', () => {
      const moduleId = 'module-1';
      const requestedPolicies = [];
      const supportedPoliciesMap = new Map([['module-1', new Set(['canView', 'canEdit'])]]);
      const moduleInfo = { translationKey: 'module.one' };

      expect(() => {
        validateSupportedPolicies(moduleId, requestedPolicies, supportedPoliciesMap, moduleInfo);
      }).not.toThrow();
    });

    it('should handle module not in supported policies map', () => {
      const moduleId = 'module-2';
      const requestedPolicies = ['canView'];
      const supportedPoliciesMap = new Map([['module-1', new Set(['canView', 'canEdit'])]]);
      const moduleInfo = { translationKey: 'module.two' };

      expect(() => {
        validateSupportedPolicies(moduleId, requestedPolicies, supportedPoliciesMap, moduleInfo);
      }).toThrow('Unsupported policies');

      expect(RoleError.unsupportedModulePolicies).toHaveBeenCalledWith({
        module: 'module.two',
        policies: ['canView'],
      });
    });

    it('should handle multiple unsupported policies', () => {
      const moduleId = 'module-1';
      const requestedPolicies = ['canView', 'canEdit', 'canDelete', 'canAdmin'];
      const supportedPoliciesMap = new Map([['module-1', new Set(['canView'])]]);
      const moduleInfo = { translationKey: 'module.one' };

      expect(() => {
        validateSupportedPolicies(moduleId, requestedPolicies, supportedPoliciesMap, moduleInfo);
      }).toThrow('Unsupported policies');

      expect(RoleError.unsupportedModulePolicies).toHaveBeenCalledWith({
        module: 'module.one',
        policies: ['canEdit', 'canDelete', 'canAdmin'],
      });
    });
  });

  describe('validateParentPolicies', () => {
    it('should not throw error when all policies are allowed by parent', () => {
      const moduleId = 'module-1';
      const requestedPolicies = ['canView', 'canEdit'];
      const parentPolicies = new Map([['module-1', new Set(['canView', 'canEdit', 'canDelete'])]]);
      const moduleInfo = { translationKey: 'module.one' };

      expect(() => {
        validateParentPolicies(moduleId, requestedPolicies, parentPolicies, moduleInfo);
      }).not.toThrow();
    });

    it('should throw error when parent has no access to module', () => {
      const moduleId = 'module-1';
      const requestedPolicies = ['canView'];
      const parentPolicies = new Map([['module-2', new Set(['canView'])]]);
      const moduleInfo = { translationKey: 'module.one' };

      expect(() => {
        validateParentPolicies(moduleId, requestedPolicies, parentPolicies, moduleInfo);
      }).toThrow('Parent has no policies access');

      expect(RoleError.parentHasNoPoliciesAccess).toHaveBeenCalledWith({
        module: 'module.one',
      });
    });

    it('should throw error when some policies exceed parent permissions', () => {
      const moduleId = 'module-1';
      const requestedPolicies = ['canView', 'canEdit', 'canDelete'];
      const parentPolicies = new Map([['module-1', new Set(['canView', 'canEdit'])]]);
      const moduleInfo = { translationKey: 'module.one' };

      expect(() => {
        validateParentPolicies(moduleId, requestedPolicies, parentPolicies, moduleInfo);
      }).toThrow('Exceeds parent policies');

      expect(RoleError.exceedsParentPolicies).toHaveBeenCalled();
    });

    it('should not throw error when no policies to validate', () => {
      const moduleId = 'module-1';
      const requestedPolicies = [];
      const parentPolicies = new Map([['module-1', new Set(['canView', 'canEdit'])]]);
      const moduleInfo = { translationKey: 'module.one' };

      expect(() => {
        validateParentPolicies(moduleId, requestedPolicies, parentPolicies, moduleInfo);
      }).not.toThrow();
    });

    it('should handle parent with limited permissions', () => {
      const moduleId = 'module-1';
      const requestedPolicies = ['canView', 'canEdit'];
      const parentPolicies = new Map([['module-1', new Set(['canView'])]]);
      const moduleInfo = { translationKey: 'module.one' };

      expect(() => {
        validateParentPolicies(moduleId, requestedPolicies, parentPolicies, moduleInfo);
      }).toThrow('Exceeds parent policies');

      expect(RoleError.exceedsParentPolicies).toHaveBeenCalled();
    });

    it('should handle parent with empty policy set', () => {
      const moduleId = 'module-1';
      const requestedPolicies = ['canView'];
      const parentPolicies = new Map([['module-1', new Set()]]);
      const moduleInfo = { translationKey: 'module.one' };

      expect(() => {
        validateParentPolicies(moduleId, requestedPolicies, parentPolicies, moduleInfo);
      }).toThrow('Exceeds parent policies');

      expect(RoleError.exceedsParentPolicies).toHaveBeenCalled();
    });

    it('should handle empty parent policies map', () => {
      const moduleId = 'module-1';
      const requestedPolicies = ['canView'];
      const parentPolicies = new Map();
      const moduleInfo = { translationKey: 'module.one' };

      expect(() => {
        validateParentPolicies(moduleId, requestedPolicies, parentPolicies, moduleInfo);
      }).toThrow('Parent has no policies access');

      expect(RoleError.parentHasNoPoliciesAccess).toHaveBeenCalledWith({
        module: 'module.one',
      });
    });

    it('should validate subset of parent policies correctly', () => {
      const moduleId = 'module-1';
      const requestedPolicies = ['canView'];
      const parentPolicies = new Map([
        ['module-1', new Set(['canView', 'canEdit', 'canDelete', 'canManage'])],
      ]);
      const moduleInfo = { translationKey: 'module.one' };

      expect(() => {
        validateParentPolicies(moduleId, requestedPolicies, parentPolicies, moduleInfo);
      }).not.toThrow();
    });
  });

  describe('validateHierarchy', () => {
    describe('root user hierarchy', () => {
      it('should allow root users to update root roles within their path', () => {
        expect(validateHierarchy('root', 'root.admin', 'root', 'root')).toBe(true);
        expect(validateHierarchy('root.admin', 'root.admin.user', 'root', 'root')).toBe(true);
        expect(validateHierarchy('root', 'root', 'root', 'root')).toBe(true);
        expect(validateHierarchy('root.admin', 'root.manager', 'root', 'root')).toBe(false);
      });

      it('should allow root users to update any organisation role', () => {
        expect(validateHierarchy('root', 'org.admin', 'root', 'organisation')).toBe(true);
        expect(validateHierarchy('root.admin', 'org.manager', 'root', 'organisation')).toBe(true);
      });

      it('should allow root users to update any merchant role', () => {
        expect(validateHierarchy('root', 'merchant.admin', 'root', 'merchant')).toBe(true);
        expect(validateHierarchy('root.admin', 'merchant.manager', 'root', 'merchant')).toBe(true);
      });
    });

    describe('organisation user hierarchy', () => {
      it('should not allow organisation users to update root roles', () => {
        expect(validateHierarchy('org.admin', 'root.admin', 'organisation', 'root')).toBe(false);
        expect(validateHierarchy('org.manager', 'root', 'organisation', 'root')).toBe(false);
      });

      it('should allow organisation users to update organisation roles within their path', () => {
        expect(
          validateHierarchy('org.admin', 'org.admin.user', 'organisation', 'organisation'),
        ).toBe(true);
        expect(validateHierarchy('org', 'org.admin', 'organisation', 'organisation')).toBe(true);
        expect(validateHierarchy('org.admin', 'org.manager', 'organisation', 'organisation')).toBe(
          false,
        );
      });

      it('should allow organisation users to update any merchant role', () => {
        expect(validateHierarchy('org.admin', 'merchant.admin', 'organisation', 'merchant')).toBe(
          true,
        );
        expect(validateHierarchy('org.manager', 'merchant.user', 'organisation', 'merchant')).toBe(
          true,
        );
      });
    });

    describe('merchant user hierarchy', () => {
      it('should not allow merchant users to update root roles', () => {
        expect(validateHierarchy('merchant.admin', 'root.admin', 'merchant', 'root')).toBe(false);
        expect(validateHierarchy('merchant.manager', 'root', 'merchant', 'root')).toBe(false);
      });

      it('should not allow merchant users to update organisation roles', () => {
        expect(validateHierarchy('merchant.admin', 'org.admin', 'merchant', 'organisation')).toBe(
          false,
        );
        expect(validateHierarchy('merchant.manager', 'org.user', 'merchant', 'organisation')).toBe(
          false,
        );
      });

      it('should allow merchant users to update merchant roles within their path', () => {
        expect(
          validateHierarchy('merchant.admin', 'merchant.admin.user', 'merchant', 'merchant'),
        ).toBe(true);
        expect(validateHierarchy('merchant', 'merchant.admin', 'merchant', 'merchant')).toBe(true);
        expect(
          validateHierarchy('merchant.admin', 'merchant.manager', 'merchant', 'merchant'),
        ).toBe(false);
      });
    });

    describe('default cases', () => {
      it('should return false for unknown user hierarchy', () => {
        expect(validateHierarchy('unknown.admin', 'merchant.user', 'unknown', 'merchant')).toBe(
          false,
        );
        expect(validateHierarchy('invalid.path', 'root.admin', 'invalid', 'root')).toBe(false);
      });

      it('should return false for organisation user with unknown role hierarchy', () => {
        expect(validateHierarchy('org.admin', 'unknown.role', 'organisation', 'unknown')).toBe(
          false,
        );
        expect(validateHierarchy('org.manager', 'custom.role', 'organisation', 'custom')).toBe(
          false,
        );
      });

      it('should return false for merchant user with non-merchant role hierarchy', () => {
        expect(validateHierarchy('merchant.admin', 'custom.role', 'merchant', 'custom')).toBe(
          false,
        );
        expect(validateHierarchy('merchant.manager', 'unknown.role', 'merchant', 'unknown')).toBe(
          false,
        );
      });
    });
  });
});
