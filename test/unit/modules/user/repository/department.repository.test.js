import { beforeEach, describe, expect, it, vi } from 'vitest';

import * as departmentRepository from '#src/modules/user/repository/department.repository.js';
import { applyOffsetPagination } from '#src/utils/pagination.util.js';
import { buildWhereFromFilters } from '#src/utils/query.util.js';
import { populateToJSON } from '#src/utils/test-helper.util.js';

vi.mock('#src/utils/pagination.util.js');
vi.mock('#src/utils/query.util.js');

describe('Department Repository', () => {
  let mockServer;
  let mockModelData;

  beforeEach(() => {
    mockServer = {
      psql: {
        Department: {
          findByPk: vi.fn(),
          findAll: vi.fn(),
          findOne: vi.fn(),
          create: vi.fn(),
        },
        DepartmentModule: {
          findOne: vi.fn(),
          create: vi.fn(),
        },
        Policy: {
          create: vi.fn(),
          findOrCreate: vi.fn(),
        },
      },
    };

    mockModelData = {
      destroy: vi.fn(),
      update: vi.fn(),
    };

    vi.clearAllMocks();
  });

  describe('findAll', () => {
    it('should call buildWhereFromFilters and applyOffsetPagination with correct parameters', async () => {
      const query = { page: 1, limit: 10 };
      const whereFilter = { status: 'active' };
      const includeFilter = [{ model: 'SomeModel' }];

      buildWhereFromFilters.mockReturnValue({ where: whereFilter, include: includeFilter });

      await departmentRepository.findAll(mockServer, query);

      expect(buildWhereFromFilters).toHaveBeenCalledWith(query, mockServer.psql.Department);
      expect(applyOffsetPagination).toHaveBeenCalledWith(
        mockServer,
        mockServer.psql.Department,
        query,
        whereFilter,
        includeFilter,
      );
    });
  });

  describe('findById', () => {
    it('should call findOne with associations when withAssoc is true (default)', async () => {
      const id = '123';
      const options = { someOption: true };

      await departmentRepository.findById(mockServer, id, true, options);

      expect(mockServer.psql.Department.findOne).toHaveBeenCalledWith({
        where: { id },
        include: [
          {
            association: 'modulePolicies',
            include: [{ association: 'module' }, { association: 'policy' }],
          },
        ],
        someOption: true,
      });
    });

    it('should call findOne with associations when withAssoc is not provided (defaults to true)', async () => {
      const id = '123';

      await departmentRepository.findById(mockServer, id);

      expect(mockServer.psql.Department.findOne).toHaveBeenCalledWith({
        where: { id },
        include: [
          {
            association: 'modulePolicies',
            include: [{ association: 'module' }, { association: 'policy' }],
          },
        ],
      });
    });

    it('should call findOne without modulePolicies association when withAssoc is false', async () => {
      const id = '123';
      const options = { someOption: true };

      await departmentRepository.findById(mockServer, id, false, options);

      expect(mockServer.psql.Department.findOne).toHaveBeenCalledWith({
        where: { id },
        include: [],
        someOption: true,
      });
    });

    it('should merge custom where conditions with id', async () => {
      const id = '123';
      const options = {
        where: { entityId: '456', status: 'active' },
        someOption: true,
      };

      await departmentRepository.findById(mockServer, id, true, options);

      expect(mockServer.psql.Department.findOne).toHaveBeenCalledWith({
        where: {
          id: '123',
          entityId: '456',
          status: 'active',
        },
        include: [
          {
            association: 'modulePolicies',
            include: [{ association: 'module' }, { association: 'policy' }],
          },
        ],
        someOption: true,
      });
    });

    it('should return the result from findOne', async () => {
      const id = '123';
      const mockDepartment = { id: '123', name: 'Test Department' };

      mockServer.psql.Department.findOne.mockResolvedValue(mockDepartment);

      const result = await departmentRepository.findById(mockServer, id);

      expect(result).toEqual(mockDepartment);
    });

    it('should return null when department is not found', async () => {
      const id = '999';

      mockServer.psql.Department.findOne.mockResolvedValue(null);

      const result = await departmentRepository.findById(mockServer, id);

      expect(result).toBeNull();
    });
  });

  describe('findAllModulePolicies', () => {
    it('should call findAll with correct parameters', async () => {
      const departmentId = '123';
      const options = { transaction: {} };

      // Mock the findAll method
      mockServer.psql.DepartmentModule.findAll = vi.fn().mockResolvedValue([]);

      await departmentRepository.findAllModulePolicies(mockServer, departmentId, options);

      expect(mockServer.psql.DepartmentModule.findAll).toHaveBeenCalledWith({
        where: { departmentId },
        include: [{ model: mockServer.psql.Policy, as: 'policy' }],
        transaction: options.transaction,
      });
    });

    it('should return the result of findAll', async () => {
      const departmentId = '123';
      const mockModules = [
        { id: '1', name: 'Module 1' },
        { id: '2', name: 'Module 2' },
      ];

      mockServer.psql.DepartmentModule.findAll = vi.fn().mockResolvedValue(mockModules);

      const result = await departmentRepository.findAllModulePolicies(mockServer, departmentId);

      expect(result).toEqual(mockModules);
    });

    it('should work without options parameter', async () => {
      const departmentId = '123';

      mockServer.psql.DepartmentModule.findAll = vi.fn().mockResolvedValue([]);

      await departmentRepository.findAllModulePolicies(mockServer, departmentId);

      expect(mockServer.psql.DepartmentModule.findAll).toHaveBeenCalledWith({
        where: { departmentId },
        include: [{ model: mockServer.psql.Policy, as: 'policy' }],
        transaction: undefined,
      });
    });
  });

  describe('createDepartment', () => {
    it('should call create with correct parameters', async () => {
      const data = { name: 'New Department' };
      const options = { someOption: true };

      await departmentRepository.createDepartment(mockServer, data, options);

      expect(mockServer.psql.Department.create).toHaveBeenCalledWith(data, options);
    });
  });

  describe('createDepartmentModule', () => {
    it('should call create with correct parameters', async () => {
      const data = { departmentId: '123', moduleId: '456' };
      const options = { someOption: true };

      await departmentRepository.createDepartmentModule(mockServer, data, options);

      expect(mockServer.psql.DepartmentModule.create).toHaveBeenCalledWith(data, options);
    });
  });

  describe('update', () => {
    it('should call update on the model with provided data and options', async () => {
      const updateData = { name: 'Updated Department' };
      const options = { someOption: true };

      await departmentRepository.update(mockModelData, updateData, options);

      expect(mockModelData.update).toHaveBeenCalledWith(updateData, options);
    });
  });

  describe('remove', () => {
    it('should call destroy on the model with provided options', async () => {
      const options = { someOption: true };

      await departmentRepository.remove(mockModelData, options);

      expect(mockModelData.destroy).toHaveBeenCalledWith(options);
    });
  });

  describe('createPolicy', () => {
    it('should call create with correct parameters', async () => {
      const data = { parentId: '123', view: true, edit: false };
      const options = { someOption: true };

      await departmentRepository.createPolicy(mockServer, data, options);

      expect(mockServer.psql.Policy.create).toHaveBeenCalledWith(data, options);
    });
  });

  describe('findDepartmentModule', () => {
    it('should call findOne with correct parameters', async () => {
      const departmentId = '123';
      const moduleId = '456';
      const options = { someOption: true };

      await departmentRepository.findDepartmentModule(mockServer, departmentId, moduleId, options);

      expect(mockServer.psql.DepartmentModule.findOne).toHaveBeenCalledWith({
        where: {
          departmentId,
          moduleId,
        },
        someOption: true,
      });
    });

    it('should return the result of findOne', async () => {
      const departmentId = '123';
      const moduleId = '456';
      const mockModule = { id: '1', name: 'Test Module' };

      mockServer.psql.DepartmentModule.findOne.mockResolvedValue(mockModule);

      const result = await departmentRepository.findDepartmentModule(
        mockServer,
        departmentId,
        moduleId,
      );

      expect(result).toEqual(mockModule);
    });

    it('should work without options parameter', async () => {
      const departmentId = '123';
      const moduleId = '456';

      await departmentRepository.findDepartmentModule(mockServer, departmentId, moduleId);

      expect(mockServer.psql.DepartmentModule.findOne).toHaveBeenCalledWith({
        where: {
          departmentId,
          moduleId,
        },
      });
    });
  });

  describe('upsertPolicy', () => {
    it('should create a new policy if it does not exist', async () => {
      const policyData = {
        parentId: '123',
        view: true,
        edit: false,
      };
      const options = { transaction: {}, authInfoId: '456' };

      const mockPolicy = populateToJSON({
        ...policyData,
        id: '789',
      });

      mockServer.psql.Policy.findOrCreate.mockResolvedValue([mockPolicy, true]);

      const result = await departmentRepository.upsertPolicy(mockServer, policyData, options);

      expect(mockServer.psql.Policy.findOrCreate).toHaveBeenCalledWith({
        where: { parentId: '123' },
        defaults: { view: true, edit: false },
        transaction: {},
        authInfoId: '456',
      });
      expect(result).toEqual({
        created: true,
        diff: {
          afterState: {
            edit: false,
            id: '789',
            parentId: '123',
            view: true,
          },
        },
        policy: mockPolicy,
        isDirty: true,
      });
    });

    it('should update an existing policy if it exists', async () => {
      const policyData = {
        parentId: '123',
        view: true,
        edit: false,
      };
      const options = { transaction: {}, authInfoId: '456' };

      const existingPolicy = populateToJSON({
        id: '789',
        parentId: '123',
        view: false,
        edit: true,
        update: vi.fn().mockResolvedValue(populateToJSON({ isDirty: true })),
      });

      mockServer.psql.Policy.findOrCreate.mockResolvedValue([existingPolicy, false]);

      const result = await departmentRepository.upsertPolicy(mockServer, policyData, options);

      expect(mockServer.psql.Policy.findOrCreate).toHaveBeenCalledWith({
        where: { parentId: '123' },
        defaults: { view: true, edit: false },
        transaction: {},
        authInfoId: '456',
      });
      expect(existingPolicy.update).toHaveBeenCalledWith({ view: true, edit: false }, options);

      expect(result.created).toBe(false);
      expect(result.policy).toEqual(existingPolicy);
      expect(result.isDirty).toBe(true);
    });

    it('should return isDirty as false if no changes were made to an existing policy', async () => {
      const policyData = {
        parentId: '123',
        view: true,
        edit: false,
      };
      const options = { transaction: {}, authInfoId: '456' };

      const existingPolicy = populateToJSON({
        id: '789',
        parentId: '123',
        view: true,
        edit: false,
        update: vi.fn().mockResolvedValue(populateToJSON({ isDirty: false })),
      });

      mockServer.psql.Policy.findOrCreate.mockResolvedValue([existingPolicy, false]);

      const result = await departmentRepository.upsertPolicy(mockServer, policyData, options);

      expect(mockServer.psql.Policy.findOrCreate).toHaveBeenCalledWith({
        where: { parentId: '123' },
        defaults: { view: true, edit: false },
        transaction: {},
        authInfoId: '456',
      });
      expect(existingPolicy.update).toHaveBeenCalledWith({ view: true, edit: false }, options);

      expect(result.created).toBe(false);
      expect(result.policy).toEqual(existingPolicy);
      expect(result.isDirty).toBe(false);
    });

    it('shold return diff=null if there is not fieldsChanged', async () => {
      const updateFnMock = vi.fn();
      const policyData = { id: '789', parentId: '123', view: true, edit: false };

      const options = { transaction: {}, authInfoId: '456' };

      const existingPolicy = populateToJSON({
        id: '789',
        parentId: '123',
        view: true,
        edit: false,
        update: updateFnMock.mockResolvedValue(
          populateToJSON({
            id: '789',
            parentId: '123',
            view: true,
            edit: false,
            update: updateFnMock,
          }),
        ),
      });

      mockServer.psql.Policy.findOrCreate.mockResolvedValue([existingPolicy, false]);

      const result = await departmentRepository.upsertPolicy(mockServer, policyData, options);

      expect(result.diff).toBeNull();
    });
  });

  describe('removeDepartmentModule', () => {
    it('should remove Policy and DepartmentModule with the given ID', async () => {
      const departmentModuleId = '123';
      const options = { transaction: {} };

      // Mock the destroy methods
      mockServer.psql.Policy.destroy = vi.fn().mockResolvedValue(1);
      mockServer.psql.DepartmentModule.destroy = vi.fn().mockResolvedValue(1);

      const result = await departmentRepository.removeDepartmentModule(
        mockServer,
        departmentModuleId,
        options,
      );

      // Check if Policy.destroy was called with correct parameters
      expect(mockServer.psql.Policy.destroy).toHaveBeenCalledWith({
        where: { parentId: departmentModuleId },
        transaction: options.transaction,
      });

      // Check if DepartmentModule.destroy was called with correct parameters
      expect(mockServer.psql.DepartmentModule.destroy).toHaveBeenCalledWith({
        where: { id: departmentModuleId },
        transaction: options.transaction,
      });

      // Check if the function returns the correct result
      expect(result).toBe(1);
    });

    it('should work without options parameter', async () => {
      const departmentModuleId = '123';

      // Mock the destroy methods
      mockServer.psql.Policy.destroy = vi.fn().mockResolvedValue(1);
      mockServer.psql.DepartmentModule.destroy = vi.fn().mockResolvedValue(1);

      await departmentRepository.removeDepartmentModule(mockServer, departmentModuleId);

      // Check if Policy.destroy was called with correct parameters
      expect(mockServer.psql.Policy.destroy).toHaveBeenCalledWith({
        where: { parentId: departmentModuleId },
        transaction: undefined,
      });

      // Check if DepartmentModule.destroy was called with correct parameters
      expect(mockServer.psql.DepartmentModule.destroy).toHaveBeenCalledWith({
        where: { id: departmentModuleId },
        transaction: undefined,
      });
    });

    it('should return 0 if no DepartmentModule was deleted', async () => {
      const departmentModuleId = '123';

      // Mock the destroy methods
      mockServer.psql.Policy.destroy = vi.fn().mockResolvedValue(0);
      mockServer.psql.DepartmentModule.destroy = vi.fn().mockResolvedValue(0);

      const result = await departmentRepository.removeDepartmentModule(
        mockServer,
        departmentModuleId,
      );

      expect(result).toBe(0);
    });
  });
});
