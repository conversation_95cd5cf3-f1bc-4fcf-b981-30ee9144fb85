import { describe, expect, it } from 'vitest';

import { COMMON_STATUSES, MODULE_NAMES } from '#src/modules/core/constants/core.constant.js';
import { CoreSchema } from '#src/modules/core/schemas/index.js';
import * as roleSchema from '#src/modules/user/schemas/role.schema.js';

describe('Role Schema Module', () => {
  const { ROLE } = MODULE_NAMES;
  const TAGS = ['BO / User Management / Access Control / Roles'];

  it('should have correct tags and summary for index schema', () => {
    expect(roleSchema.index.tags).toEqual(TAGS);
    expect(roleSchema.index.summary).toBe(`Get a list of ${ROLE}`);
  });

  it('should have correct query parameters for index schema', () => {
    const querystring = roleSchema.index.querystring;
    expect(querystring.type).toBe('object');
    expect(querystring.properties).toHaveProperty('filter_name_eq');
    expect(querystring.properties).toHaveProperty('filter_parentId_eq');
    expect(querystring.properties).toHaveProperty('filter_departmentId_eq');
    expect(querystring.properties).toHaveProperty('filter_status_eq');
  });

  it('should have correct response schema for index', () => {
    const response = roleSchema.index.response;
    expect(response).toHaveProperty('200');
    expect(response['200'].type).toBe('object');
    expect(response['200'].properties).toHaveProperty('message');
    expect(response['200'].properties).toHaveProperty('data');
    expect(response['200'].properties).toHaveProperty('meta');
    expect(response['200'].properties.data.type).toBe('array');
  });

  it('should have correct tags and summary for view schema', () => {
    expect(roleSchema.view.tags).toEqual(TAGS);
    expect(roleSchema.view.summary).toBe(`View a ${ROLE}`);
  });

  it('should have correct params for view schema', () => {
    expect(roleSchema.view.params).toEqual(CoreSchema.REQ_PARAM_UUID);
  });

  it('should have correct response schema for view', () => {
    const response = roleSchema.view.response;
    expect(response).toHaveProperty('200');
    expect(response['200'].properties.data.properties).toHaveProperty('name');
    expect(response['200'].properties.data.properties).toHaveProperty('description');
    expect(response['200'].properties.data.properties).toHaveProperty('status');
    expect(response['200'].properties.data.properties).toHaveProperty('parentName');
    expect(response['200'].properties.data.properties).toHaveProperty('modules');
    expect(response['200'].properties.data.properties).toHaveProperty('metadata');
    expect(response['200'].properties.data.properties).toHaveProperty('version');
  });

  it('should have correct tags and summary for create schema', () => {
    expect(roleSchema.create.tags).toEqual(TAGS);
    expect(roleSchema.create.summary).toBe(`Create a ${ROLE}`);
  });

  it('should have correct body schema for create', () => {
    const body = roleSchema.create.body;
    expect(body.type).toBe('object');
    expect(body.properties).toHaveProperty('name');
    expect(body.properties).toHaveProperty('description');
    expect(body.properties).toHaveProperty('parentId');
    expect(body.properties).toHaveProperty('departmentId');
    expect(body.properties).toHaveProperty('modules');
    expect(body.required).toContain('name');
    expect(body.required).toContain('modules');
  });

  it('should have correct response schema for create', () => {
    expect(roleSchema.create.response).toEqual(CoreSchema.CREATE_RESPONSE);
  });

  it('should have correct tags and summary for update schema', () => {
    expect(roleSchema.update.tags).toEqual(TAGS);
    expect(roleSchema.update.summary).toBe(`Update a ${ROLE}`);
  });

  it('should have correct params for update schema', () => {
    expect(roleSchema.update.params).toEqual(CoreSchema.REQ_PARAM_UUID);
  });

  it('should have correct body schema for update', () => {
    const body = roleSchema.update.body;
    expect(body.type).toBe('object');
    expect(body.properties).toHaveProperty('name');
    expect(body.properties).toHaveProperty('description');
    expect(body.properties).toHaveProperty('modules');
    expect(body.properties).toHaveProperty('version');
    expect(body.required).toContain('name');
    expect(body.required).toContain('modules');
    expect(body.required).toContain('version');
  });

  it('should have correct response schema for update', () => {
    expect(roleSchema.update.response).toEqual(CoreSchema.UPDATE_RESPONSE);
  });

  it('should have correct tags and summary for updateStatus schema', () => {
    expect(roleSchema.updateStatus.tags).toEqual(TAGS);
    expect(roleSchema.updateStatus.summary).toBe(`Update a ${ROLE} status`);
  });

  it('should have correct params for updateStatus schema', () => {
    expect(roleSchema.updateStatus.params).toEqual(CoreSchema.REQ_PARAM_UUID);
  });

  it('should have correct body schema for updateStatus', () => {
    const body = roleSchema.updateStatus.body;
    expect(body.type).toBe('object');
    expect(body.properties).toHaveProperty('status');
    expect(body.properties).toHaveProperty('version');
    expect(body.properties.status.enum).toEqual(Object.values(COMMON_STATUSES));
    expect(body.required).toContain('status');
    expect(body.required).toContain('version');
  });

  it('should have correct response schema for updateStatus', () => {
    expect(roleSchema.updateStatus.response).toEqual(CoreSchema.UPDATE_RESPONSE);
  });

  it('should define ROLE_RES_PROPERTIES with all required fields', () => {
    const response = roleSchema.view.response;
    const properties = response['200'].properties.data.properties;

    Object.keys(CoreSchema.COMMON_PROPERTIES).forEach((key) => {
      expect(properties).toHaveProperty(key);
    });

    expect(properties).toHaveProperty('name');
    expect(properties).toHaveProperty('description');
    expect(properties).toHaveProperty('status');
    expect(properties.status.enum).toEqual(Object.values(COMMON_STATUSES));
    expect(properties).toHaveProperty('parentName');
    expect(properties).toHaveProperty('modules');
    expect(properties.modules.properties).toHaveProperty('root');
    expect(properties.modules.properties).toHaveProperty('organisation');
    expect(properties.modules.properties).toHaveProperty('merchant');
    expect(properties).toHaveProperty('metadata');
    expect(properties).toHaveProperty('version');
  });

  it('should have correct format for UUID fields', () => {
    expect(roleSchema.create.body.properties.parentId.format).toBe('uuid');
    expect(roleSchema.create.body.properties.departmentId.format).toBe('uuid');
  });

  it('should have correct tags and summary for navigations schema', () => {
    expect(roleSchema.navigations.tags).toEqual(TAGS);
    expect(roleSchema.navigations.summary).toBe('Get user navigation menus based on role policies');
  });

  it('should have correct response schema for navigations', () => {
    const response = roleSchema.navigations.response;
    expect(response).toHaveProperty('200');
    expect(response['200'].type).toBe('object');
    expect(response['200'].properties).toHaveProperty('message');
    expect(response['200'].properties).toHaveProperty('data');
    expect(response['200'].properties).toHaveProperty('debug');

    const data = response['200'].properties.data;
    expect(data.type).toBe('object');
    expect(data.properties).toHaveProperty('root');
    expect(data.properties).toHaveProperty('organisation');
    expect(data.properties).toHaveProperty('merchant');

    ['root', 'organisation', 'merchant'].forEach((level) => {
      expect(data.properties[level].type).toBe('object');
      expect(data.properties[level].properties).toHaveProperty('side');
      expect(data.properties[level].properties).toHaveProperty('top');
    });
  });

  it('should define a flexible NAVIGATION_MENU_SCHEMA that supports mixed content', () => {
    const response = roleSchema.navigations.response;
    const sideMenu = response['200'].properties.data.properties.root.properties.side;

    expect(sideMenu.type).toBe('array');

    expect(sideMenu.items).toHaveProperty('oneOf');

    const menuItem = sideMenu.items;
    const oneOfOptions = menuItem.oneOf;

    // First level options: string or object
    expect(oneOfOptions[0].type).toBe('string');

    expect(oneOfOptions[1].type).toBe('object');
    expect(oneOfOptions[1].additionalProperties.type).toBe('array');

    // Second level options within the array
    const nestedItems = oneOfOptions[1].additionalProperties.items.oneOf;
    expect(nestedItems[0].type).toBe('string');
    expect(nestedItems[1].type).toBe('object');

    // Check for name/url object structure
    expect(nestedItems[1].properties).toHaveProperty('name');
    expect(nestedItems[1].properties).toHaveProperty('url');
    expect(nestedItems[1].properties.name.type).toBe('string');
    expect(nestedItems[1].properties.url.type).toBe('string');
    expect(nestedItems[1].required).toContain('name');
    expect(nestedItems[1].required).toContain('url');

    // Check for third level nesting option
    expect(nestedItems[2].type).toBe('object');
    expect(nestedItems[2].additionalProperties.type).toBe('array');

    // Check third level items
    const thirdLevelItems = nestedItems[2].additionalProperties.items.oneOf;
    expect(thirdLevelItems[0].type).toBe('string');
    expect(thirdLevelItems[1].type).toBe('object');
    expect(thirdLevelItems[1].properties).toHaveProperty('name');
    expect(thirdLevelItems[1].properties).toHaveProperty('url');
    expect(thirdLevelItems[1].required).toContain('name');
    expect(thirdLevelItems[1].required).toContain('url');
  });

  it('should have correct tags and summary for options schema', () => {
    expect(roleSchema.options.tags).toEqual(TAGS);
    expect(roleSchema.options.summary).toBe('Get available role options');
  });

  it('should have correct querystring for options schema', () => {
    const querystring = roleSchema.options.querystring;
    expect(querystring.type).toBe('object');
    expect(querystring.properties).toHaveProperty('filter_parentId_eq');
    expect(querystring.properties).toHaveProperty('filter_departmentId_eq');
    expect(querystring.properties.filter_parentId_eq.type).toBe('string');
    expect(querystring.properties.filter_departmentId_eq.type).toBe('string');
  });

  it('should have correct response schema structure for options with 200 status', () => {
    const response = roleSchema.options.response;
    expect(response).toHaveProperty('200');

    const responseSchema = response['200'];
    expect(responseSchema.description).toBe('Success response');
    expect(responseSchema.type).toBe('object');
    expect(responseSchema.properties).toHaveProperty('message');
    expect(responseSchema.properties).toHaveProperty('data');
    expect(responseSchema.properties).toHaveProperty('debug');

    expect(responseSchema.properties.message.type).toBe('string');

    const dataProperties = responseSchema.properties.data;
    expect(dataProperties.type).toBe('object');
    expect(dataProperties.properties).toHaveProperty('root');
    expect(dataProperties.properties).toHaveProperty('organisation');
    expect(dataProperties.properties).toHaveProperty('merchant');

    ['root', 'organisation', 'merchant'].forEach((level) => {
      expect(dataProperties.properties[level].type).toBe('array');
      expect(dataProperties.properties[level].items).toBeDefined();
    });

    const debugProperties = responseSchema.properties.debug;
    expect(debugProperties.type).toBe('object');
    expect(debugProperties.properties).toHaveProperty('appName');
    expect(debugProperties.properties).toHaveProperty('environment');
    expect(debugProperties.properties).toHaveProperty('requestId');
    expect(debugProperties.properties).toHaveProperty('version');

    ['appName', 'environment', 'requestId', 'version'].forEach((prop) => {
      expect(debugProperties.properties[prop].type).toBe('string');
    });
  });

  it('should have MODULE_OPTIONS_SCHEMA as items type for root array in options data', () => {
    const response = roleSchema.options.response;
    const rootArray = response['200'].properties.data.properties.root;
    expect(rootArray.type).toBe('array');
    expect(rootArray.items).toBeDefined();
    expect(rootArray.items.type).toBe('object');
    expect(rootArray.items.properties).toHaveProperty('id');
    expect(rootArray.items.properties).toHaveProperty('name');
    expect(rootArray.items.properties).toHaveProperty('hierarchy');
    expect(rootArray.items.properties).toHaveProperty('navigationPosition');
    expect(rootArray.items.properties).toHaveProperty('navigationType');
    expect(rootArray.items.properties).toHaveProperty('translationKey');
    expect(rootArray.items.properties).toHaveProperty('level');
    expect(rootArray.items.properties).toHaveProperty('parentId');
    expect(rootArray.items.properties).toHaveProperty('navigationUrl');
    expect(rootArray.items.properties).toHaveProperty('policy');
    expect(rootArray.items.properties).toHaveProperty('children');
  });

  it('should have MODULE_OPTIONS_SCHEMA as items type for organisation and merchant arrays in options data', () => {
    const response = roleSchema.options.response;
    const dataProperties = response['200'].properties.data.properties;

    // Check organisation array
    const organisationArray = dataProperties.organisation;
    expect(organisationArray.type).toBe('array');
    expect(organisationArray.items).toBeDefined();
    expect(organisationArray.items.type).toBe('object');
    expect(organisationArray.items.properties).toHaveProperty('id');
    expect(organisationArray.items.properties).toHaveProperty('name');
    expect(organisationArray.items.properties).toHaveProperty('hierarchy');
    expect(organisationArray.items.properties).toHaveProperty('navigationPosition');
    expect(organisationArray.items.properties).toHaveProperty('navigationType');
    expect(organisationArray.items.properties).toHaveProperty('translationKey');
    expect(organisationArray.items.properties).toHaveProperty('level');
    expect(organisationArray.items.properties).toHaveProperty('parentId');
    expect(organisationArray.items.properties).toHaveProperty('navigationUrl');
    expect(organisationArray.items.properties).toHaveProperty('policy');
    expect(organisationArray.items.properties).toHaveProperty('children');

    // Check merchant array
    const merchantArray = dataProperties.merchant;
    expect(merchantArray.type).toBe('array');
    expect(merchantArray.items).toBeDefined();
    expect(merchantArray.items.type).toBe('object');
    expect(merchantArray.items.properties).toHaveProperty('id');
    expect(merchantArray.items.properties).toHaveProperty('name');
    expect(merchantArray.items.properties).toHaveProperty('hierarchy');
    expect(merchantArray.items.properties).toHaveProperty('navigationPosition');
    expect(merchantArray.items.properties).toHaveProperty('navigationType');
    expect(merchantArray.items.properties).toHaveProperty('translationKey');
    expect(merchantArray.items.properties).toHaveProperty('level');
    expect(merchantArray.items.properties).toHaveProperty('parentId');
    expect(merchantArray.items.properties).toHaveProperty('navigationUrl');
    expect(merchantArray.items.properties).toHaveProperty('policy');
    expect(merchantArray.items.properties).toHaveProperty('children');
  });
});
