import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

import { CoreError } from '#src/modules/core/errors/index.js';
import { ModuleRepository } from '#src/modules/core/repository/index.js';
import { RoleValidation } from '#src/modules/core/validations/index.js';
import { RoleError } from '#src/modules/user/errors/index.js';
import {
  DepartmentRepository,
  PolicyRepository,
  RoleModuleRepository,
  RoleRepository,
} from '#src/modules/user/repository/index.js';
import { getModulePolicyOptions } from '#src/modules/user/services/department.service.js';
import {
  create,
  index,
  navigations,
  options,
  update,
  updateStatus,
  view,
} from '#src/modules/user/services/role.service.js';
import { clearCacheWithPrefix, generateCacheKey } from '#src/utils/cache.util.js';
import { withTransaction } from '#src/utils/db-transaction.util.js';

vi.mock('#src/modules/user/services/policy.service.js', () => ({
  updatePolicies: vi.fn(),
  updateDescendantPolicies: vi.fn(),
}));

vi.mock('#src/modules/user/repository/index.js', () => ({
  DepartmentRepository: {
    findById: vi.fn(),
  },

  RoleModuleRepository: {
    bulkCreate: vi.fn(),
    bulkUpsert: vi.fn(),
    findAllByRoleIdWithPolicyAndModules: vi.fn(),
    findOrCreate: vi.fn(),
    update: vi.fn(),
    findAllByRoleIdWithPolicy: vi.fn(),
    findAllByRoleIdsAndModuleId: vi.fn(),
  },
  RoleRepository: {
    findById: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
    findAll: vi.fn(),
    findByIdWithModulePolicies: vi.fn(),
    findDescendantsByParentPath: vi.fn(),
    findAllByParentId: vi.fn(),
    findByEntityIdAndName: vi.fn(),
  },
  PolicyRepository: {
    bulkCreate: vi.fn(),
    bulkUpsert: vi.fn(),
    findByParentId: vi.fn(),
    findByParentIds: vi.fn(),
    upsert: vi.fn(),
  },
}));

vi.mock('#src/modules/core/repository/index.js', () => ({
  ModuleRepository: {
    findAll: vi.fn(),
    findById: vi.fn(),
    findModulePolicies: vi.fn(),
  },
}));

vi.mock('#src/utils/cache.util.js', () => ({
  clearCache: vi.fn(),
  clearCacheWithPrefix: vi.fn(),
  generateCacheKey: vi.fn().mockReturnValue('test-cache-key'),
}));
vi.mock('#src/modules/core/validations/index.js');
vi.mock('#src/modules/user/services/department.service.js');
vi.mock('#src/utils/db-transaction.util.js');

vi.mock('#src/modules/core/constants/index.js', () => ({
  CoreConstant: {
    MODULE_NAMES: { ROLE: 'ROLE' },
    MODULE_METHODS: {
      INDEX: 'INDEX',
      VIEW: 'VIEW',
      OPTION: 'OPTION',
      NAVIGATION: 'NAVIGATION',
    },
    COMMON_STATUSES: { ACTIVE: 'active', INACTIVE: 'active' },
    REMARK_STATUSES: { ACTIVE: 'active', INACTIVE: 'active' },
    CACHE_SECOND: { DAILY: 86400 },
    CACHE_KEY_SEGMENTS: {
      ENTITY: 'entity',
      AUTH: 'auth',
      AUDIT_TRAIL: 'auditTrail',
    },
  },
}));

vi.mock('#src/modules/user/constants/index.js', () => ({
  RoleConstant: {
    POLICIES: ['canWrite', 'canView', 'canEdit'],
  },
  DepartmentConstant: {
    POLICIES: ['canWrite', 'canView', 'canEdit'],
  },
  UserConstant: {
    POLICIES: ['canWrite', 'canView', 'canEdit'],
  },
}));

vi.mock('#src/modules/user/constants/department.constant.js', () => ({
  NAVIGATION_TYPES: {
    SIDE: 'SIDE',
    TOP: 'TOP',
    PERSONAL: 'personal',
    NONE: 'none',
  },
}));

describe('Role Service', () => {
  let mockServer;
  let mockRequest;
  let mockTransaction;

  beforeEach(() => {
    mockTransaction = {};
    mockServer = {
      redis: {
        del: vi.fn(),
      },
      psql: {
        connection: {
          transaction: vi.fn().mockImplementation((callback) => callback(mockTransaction)),
          models: {
            Role: {
              findOne: vi.fn(),
              findAll: vi.fn(),
            },
            RoleModulePolicy: {
              findAll: vi.fn(),
              findOrCreate: vi.fn(),
            },
            Policy: {
              findOne: vi.fn(),
              findOrCreate: vi.fn(),
              upsert: vi.fn(),
            },
            Module: {
              findAll: vi.fn(),
              findByPk: vi.fn(),
            },
          },
          Sequelize: {
            Op: {
              ne: Symbol('ne'),
            },
          },
        },
      },
    };

    mockRequest = {
      server: mockServer,
      entity: { id: 'entity-1', hierarchy: 'root' },
      params: { id: 'role-1' },
      query: {},
      body: {},
      authInfo: { id: 'user-1', roleId: 'role-1' },
      userEntity: { id: 'entity-1', hierarchy: 'root' },
    };

    const descendantRoleModules = [
      { id: 'descendant-role-module-1', roleId: 'descendant-1', moduleId: 'module-1' },
      { id: 'descendant-role-module-2', roleId: 'descendant-2', moduleId: 'module-1' },
    ];
    RoleModuleRepository.findAllByRoleIdsAndModuleId = vi
      .fn()
      .mockResolvedValue(descendantRoleModules);

    const updateDescendantPolicies = [
      {
        id: 'descendant-policy-1',
        parentId: 'descendant-role-module-1',
        canView: true,
        canWrite: false,
        canEdit: true,
        toJSON: () => ({
          id: 'descendant-policy-1',
          parentId: 'descendant-role-module-1',
          canView: true,
          canWrite: false,
          canEdit: true,
        }),
      },
      {
        id: 'descendant-policy-2',
        parentId: 'descendant-role-module-2',
        canView: true,
        canWrite: true,
        canEdit: false,
        toJSON: () => ({
          id: 'descendant-policy-2',
          parentId: 'descendant-role-module-2',
          canView: true,
          canWrite: true,
          canEdit: false,
        }),
      },
    ];
    PolicyRepository.findByParentIds = vi.fn().mockResolvedValue(updateDescendantPolicies);

    clearCacheWithPrefix.mockResolvedValue();
    generateCacheKey.mockReturnValue('test-cache-key');
    withTransaction.mockImplementation((_, __, callback) => callback(mockTransaction));
    getModulePolicyOptions.mockResolvedValue([]);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('index', () => {
    it('should return all roles for an entity', async () => {
      const mockRoles = [{ id: 'role-1' }, { id: 'role-2' }];
      RoleRepository.findAll.mockResolvedValue(mockRoles);

      const result = await index(mockRequest);

      expect(RoleRepository.findAll).toHaveBeenCalledWith(mockServer, {
        filter_entityId_eq: 'entity-1',
      });
      expect(result).toEqual(mockRoles);
    });
  });

  describe('view', () => {
    it('should return a specific role by ID', async () => {
      const mockRole = { id: 'role-1', name: 'Admin' };
      RoleRepository.findByIdWithModulePolicies.mockResolvedValue(mockRole);

      const result = await view(mockRequest);

      expect(RoleRepository.findByIdWithModulePolicies).toHaveBeenCalledWith(
        mockServer,
        'entity-1',
        'role-1',
      );
      expect(result).toEqual(mockRole);
    });

    it('should throw an error if role is not found', async () => {
      RoleRepository.findByIdWithModulePolicies.mockResolvedValue(null);
      CoreError.dataNotFound = vi.fn().mockReturnValue(new Error('Role not found'));

      await expect(view(mockRequest)).rejects.toThrow('Role not found');
      expect(CoreError.dataNotFound).toHaveBeenCalledWith({
        data: 'common.label.role',
        attribute: 'ID',
        value: 'role-1',
      });
    });
  });

  describe('create', () => {
    beforeEach(() => {
      mockRequest.body = {
        name: 'New Role',
        description: 'Role description',
        status: 'active',
        modules: [{ moduleId: 'module-1', policies: ['canView', 'canWrite'] }],
      };

      RoleValidation.validateRoleName.mockResolvedValue(true);
      RoleValidation.validateAndFilterModulePolicies.mockResolvedValue([
        { moduleId: 'module-1', policies: ['canView', 'canWrite'] },
      ]);

      RoleRepository.create.mockResolvedValue({
        id: 'new-role-1',
        name: 'New Role',
        path: 'New Role',
        toJSON: () => ({
          id: 'new-role-1',
          name: 'New Role',
          path: 'New Role',
        }),
      });

      RoleModuleRepository.bulkCreate.mockResolvedValue([
        {
          id: 'role-module-1',
          roleId: 'new-role-1',
          moduleId: 'module-1',
          toJSON: () => ({
            id: 'role-module-1',
            roleId: 'new-role-1',
            moduleId: 'module-1',
          }),
        },
      ]);

      PolicyRepository.bulkCreate.mockResolvedValue([
        {
          id: 'policy-1',
          parentId: 'role-module-1',
          canView: true,
          canWrite: true,
          toJSON: () => ({
            id: 'policy-1',
            parentId: 'role-module-1',
            canView: true,
            canWrite: true,
          }),
        },
      ]);
    });

    it('should return early with empty arrays when filteredModules length is 0', async () => {
      // Call the private function directly by importing it or accessing it through the module
      // Since createRoleModulePolicies is private, we need to test it through the create function
      mockRequest.body = {
        name: 'Test Role',
        modules: [], // This will result in filteredModules being empty
      };

      RoleValidation.validateRoleName.mockResolvedValue(true);
      RoleValidation.validateAndFilterModulePolicies.mockResolvedValue([]);

      RoleRepository.create.mockResolvedValue({
        id: 'new-role-1',
        name: 'Test Role',
        path: 'Test Role',
        toJSON: () => ({
          id: 'new-role-1',
          name: 'Test Role',
          path: 'Test Role',
        }),
      });

      const result = await create(mockRequest);

      expect(RoleModuleRepository.bulkCreate).not.toHaveBeenCalled();
      expect(PolicyRepository.bulkCreate).not.toHaveBeenCalled();
      expect(result.auditModelMapping.RoleModule).toBeUndefined();
      expect(result.auditModelMapping.Policy).toBeUndefined();
      expect(result.result).toEqual(
        expect.objectContaining({
          id: 'new-role-1',
          name: 'Test Role',
          path: 'Test Role',
        }),
      );
    });

    it('should create a new role without parent', async () => {
      const result = await create(mockRequest);

      expect(RoleValidation.validateRoleName).toHaveBeenCalledWith(
        mockServer,
        'entity-1',
        'New Role',
      );

      expect(RoleRepository.create).toHaveBeenCalledWith(
        mockServer,
        {
          parentId: null,
          entityId: 'entity-1',
          departmentId: undefined,
          name: 'New Role',
          description: 'Role description',
          status: 'active',
          path: 'new_role',
        },
        { transaction: mockTransaction, authInfoId: 'user-1' },
      );

      expect(RoleModuleRepository.bulkCreate).toHaveBeenCalledWith(
        mockServer,
        [
          {
            roleId: 'new-role-1',
            moduleId: 'module-1',
          },
        ],
        { transaction: mockTransaction, authInfoId: 'user-1' },
      );

      expect(PolicyRepository.bulkCreate).toHaveBeenCalledWith(
        mockServer,
        [
          {
            parentId: 'role-module-1',
            canView: true,
            canWrite: true,
          },
        ],
        { transaction: mockTransaction, authInfoId: 'user-1' },
      );

      expect(clearCacheWithPrefix).toHaveBeenCalled();
      expect(result).toEqual({
        result: expect.objectContaining({
          id: 'new-role-1',
          name: 'New Role',
          path: 'New Role',
        }),
        auditModelMapping: expect.any(Object),
      });
    });

    it('should create a new role with parent', async () => {
      mockRequest.body.parentId = 'parent-1';

      const mockParentRole = {
        id: 'parent-1',
        name: 'Parent Role',
        path: 'Parent Role',
      };

      RoleRepository.findById.mockResolvedValue(mockParentRole);

      const result = await create(mockRequest);

      expect(RoleRepository.findById).toHaveBeenCalledWith(mockServer, 'parent-1', 'entity-1');
      expect(RoleRepository.create).toHaveBeenCalledWith(
        mockServer,
        {
          parentId: 'parent-1',
          entityId: 'entity-1',
          departmentId: undefined,
          name: 'New Role',
          description: 'Role description',
          status: 'active',
          path: 'Parent Role.new_role',
        },
        { transaction: mockTransaction, authInfoId: 'user-1' },
      );

      expect(result).toEqual({
        result: expect.objectContaining({
          id: 'new-role-1',
          name: 'New Role',
          path: 'New Role',
        }),
        auditModelMapping: expect.any(Object),
      });
    });

    it('should throw an error if parent role is not found', async () => {
      mockRequest.body.parentId = 'non-existent-parent';
      RoleRepository.findById.mockResolvedValue(null);
      CoreError.dataNotFound = vi.fn().mockReturnValue(new Error('Parent role not found'));

      await expect(create(mockRequest)).rejects.toThrow('Parent role not found');
      expect(CoreError.dataNotFound).toHaveBeenCalledWith({
        data: 'common.label.parentRole',
        attribute: 'ID',
        value: 'non-existent-parent',
      });
    });
  });

  describe('update', () => {
    beforeEach(() => {
      RoleModuleRepository.bulkUpsert = vi.fn().mockResolvedValue([
        {
          id: 'role-module-1',
          roleId: 'role-1',
          moduleId: 'module-1',
          toJSON: () => ({ id: 'role-module-1', roleId: 'role-1', moduleId: 'module-1' }),
        },
        {
          id: 'role-module-2',
          roleId: 'role-1',
          moduleId: 'module-2',
          toJSON: () => ({ id: 'role-module-2', roleId: 'role-1', moduleId: 'module-2' }),
        },
        {
          id: 'role-module-4',
          roleId: 'role-1',
          moduleId: 'module-4',
          toJSON: () => ({ id: 'role-module-4', roleId: 'role-1', moduleId: 'module-4' }),
        },
      ]);

      PolicyRepository.bulkUpsert = vi.fn().mockResolvedValue([
        {
          id: 'policy-1',
          parentId: 'role-module-1',
          toJSON: () => ({ id: 'policy-1', parentId: 'role-module-1' }),
        },
        {
          id: 'policy-2',
          parentId: 'role-module-2',
          toJSON: () => ({ id: 'policy-2', parentId: 'role-module-2' }),
        },
        {
          id: 'policy-4',
          parentId: 'role-module-4',
          toJSON: () => ({ id: 'policy-4', parentId: 'role-module-4' }),
        },
      ]);

      mockRequest.body = {
        name: 'Updated Role',
        description: 'Updated description',
        status: 'active',
        modules: [{ moduleId: 'module-1', policies: ['canView', 'canWrite'] }],
        version: 1,
      };

      const mockRole = {
        id: 'role-1',
        name: 'Original Role',
        path: 'Original Role',
        parentId: null,
      };

      RoleRepository.findById.mockResolvedValue(mockRole);
      RoleValidation.validateRoleName.mockResolvedValue(true);
      RoleValidation.validateHierarchy.mockReturnValue(true);
      RoleValidation.validateAndFilterModulePolicies.mockResolvedValue([
        { moduleId: 'module-1', policies: ['canView', 'canWrite'] },
      ]);

      RoleRepository.update.mockResolvedValue({
        id: 'role-1',
        name: 'Updated Role',
        path: 'Updated Role',
        toJSON: () => ({
          id: 'role-1',
          name: 'Updated Role',
          path: 'Updated Role',
        }),
      });

      RoleRepository.findDescendantsByParentPath.mockResolvedValue([]);
      RoleRepository.findAllByParentId.mockResolvedValue([]);
      RoleModuleRepository.findAllByRoleIdWithPolicy.mockResolvedValue([
        {
          id: 'existing-module-1',
          moduleId: 'module-1',
          Policy: { id: 'policy-setting-1' },
        },
      ]);

      const mockRoleModule = {
        id: 'role-module-1',
        update: vi.fn().mockResolvedValue({
          id: 'role-module-1',
          updated: true,
          toJSON: () => ({ id: 'role-module-1', updated: true }),
        }),
        toJSON: () => ({ id: 'role-module-1' }),
      };
      RoleModuleRepository.findOrCreate.mockResolvedValue([mockRoleModule, false]);

      PolicyRepository.findByParentId.mockResolvedValue(null);
      PolicyRepository.upsert.mockResolvedValue([
        {
          id: 'policy-setting-1',
          toJSON: () => ({ id: 'policy-setting-1' }),
        },
        false,
      ]);
      PolicyRepository.update = vi.fn().mockResolvedValue({ updated: true });
    });

    afterEach(() => {
      vi.clearAllMocks();
    });

    it('should update an existing role', async () => {
      const result = await update(mockRequest);

      expect(RoleRepository.findById).toHaveBeenCalledWith(mockServer, 'role-1', 'entity-1');
      expect(RoleValidation.validateRoleName).toHaveBeenCalledWith(
        mockServer,
        'entity-1',
        'Updated Role',
        'role-1',
      );

      expect(RoleRepository.update).toHaveBeenCalledWith(
        expect.objectContaining({ id: 'role-1' }),
        {
          name: 'Updated Role',
          departmentId: undefined,
          description: 'Updated description',
          status: 'active',
          path: 'Updated_Role',
          version: 1,
        },
        { transaction: mockTransaction, authInfoId: 'user-1' },
      );

      expect(clearCacheWithPrefix).toHaveBeenCalled();
      expect(result).toEqual({
        result: expect.objectContaining({
          id: 'role-1',
          name: 'Updated Role',
          path: 'Updated Role',
        }),
        auditModelMapping: expect.any(Object),
      });
    });

    it('should use original name if name is not provided in request body', async () => {
      delete mockRequest.body.name;

      const mockRole = {
        id: 'role-1',
        name: 'Original Role',
        path: 'Original Role',
        parentId: null,
      };

      RoleRepository.findById.mockResolvedValue(mockRole);

      await update(mockRequest);

      expect(RoleRepository.update).toHaveBeenCalledWith(
        expect.objectContaining({ id: 'role-1' }),
        {
          name: 'Original Role',
          departmentId: undefined,
          description: 'Updated description',
          status: 'active',
          path: 'Original Role',
          version: 1,
        },
        { transaction: mockTransaction, authInfoId: 'user-1' },
      );
    });

    it('should throw an error if role is not found', async () => {
      RoleRepository.findById.mockResolvedValueOnce(null);
      CoreError.dataNotFound = vi.fn().mockReturnValue(new Error('Role not found'));

      await expect(update(mockRequest)).rejects.toThrow('Role not found');
      expect(CoreError.dataNotFound).toHaveBeenCalledWith({
        data: 'common.label.role',
        attribute: 'ID',
        value: 'role-1',
      });
    });

    it('should throw an error if user is not authorized to update the role outside their hierarchy', async () => {
      RoleValidation.validateHierarchy.mockReturnValue(false);
      RoleError.updateNotAllowedForHierarchy = vi
        .fn()
        .mockReturnValue(new Error('You are not allowed to update a role outside your hierarchy.'));

      await expect(update(mockRequest)).rejects.toThrow(
        'You are not allowed to update a role outside your hierarchy.',
      );
      expect(RoleError.updateNotAllowedForHierarchy).toHaveBeenCalled();
    });

    it('should update descendant paths when parent role name changes', async () => {
      const descendantRole = {
        id: 'descendant-1',
        path: 'Original Role.Child',
      };

      RoleRepository.findDescendantsByParentPath = vi.fn().mockResolvedValue([descendantRole]);

      RoleRepository.findById = vi
        .fn()
        .mockResolvedValueOnce({
          id: 'role-1',
          name: 'Original Role',
          path: 'Original Role',
          parentId: null,
          departmentId: 'dept-1',
        })
        .mockResolvedValueOnce({
          id: 'user-role-1',
          path: 'Admin',
        });

      RoleValidation.validateHierarchy = vi.fn().mockReturnValue(true);
      RoleValidation.validateRoleName = vi.fn().mockResolvedValue();
      RoleValidation.validateAndFilterModulePolicies = vi.fn().mockResolvedValue([]);

      await update(mockRequest);

      expect(RoleRepository.update).toHaveBeenCalledTimes(2);

      expect(RoleRepository.update).toHaveBeenNthCalledWith(
        1,
        expect.objectContaining({ id: 'role-1' }),
        expect.objectContaining({
          name: 'Updated Role',
          path: 'Updated_Role',
        }),
        { transaction: mockTransaction, authInfoId: 'user-1' },
      );

      expect(RoleRepository.update).toHaveBeenNthCalledWith(
        2,
        descendantRole,
        { path: 'Updated_Role.Child' },
        { transaction: mockTransaction, authInfoId: 'user-1' },
      );
    });

    it('should throw an error if user role is not found', async () => {
      RoleRepository.findById.mockResolvedValueOnce({
        id: 'role-1',
        name: 'Original Role',
        path: 'Original Role',
      });

      RoleRepository.findById.mockResolvedValueOnce(null);

      CoreError.dataNotFound = vi.fn().mockReturnValue(new Error('Role not found'));

      await expect(update(mockRequest)).rejects.toThrow('Role not found');

      expect(CoreError.dataNotFound).toHaveBeenCalledWith({
        data: 'common.label.role',
        attribute: 'ID',
        value: 'role-1',
      });

      expect(RoleRepository.findById).toHaveBeenNthCalledWith(2, mockServer, 'role-1', 'entity-1');
    });

    it('should set policies to false for modules not in the request', async () => {
      mockRequest.body.modules = [{ moduleId: 'module-1', policies: ['canView', 'canWrite'] }];

      const existingPolicies = [
        {
          id: 'role-module-2',
          moduleId: 'module-2',
          policies: { id: 'policy-setting-2' },
        },
      ];

      RoleModuleRepository.findAllByRoleIdWithPolicy.mockResolvedValue(existingPolicies);

      PolicyRepository.upsert = vi.fn().mockResolvedValue({
        toJSON: () => ({}),
      });
      RoleRepository.findAllByParentId = vi.fn().mockResolvedValue([]);

      await update(mockRequest);

      expect(PolicyRepository.upsert).toHaveBeenCalledWith(
        mockServer,
        {
          parentId: 'role-module-2',
          canView: false,
          canWrite: false,
          canEdit: false,
        },
        { transaction: mockTransaction, authInfoId: 'user-1' },
      );
    });

    it('should handle modules where existingPolicies.find returns undefined due to missing policy.id', async () => {
      mockRequest.body = {
        name: 'Updated Role',
        modules: [{ moduleId: 'module-1', policies: ['canView', 'canWrite'] }],
        version: 1,
      };

      const mockRole = {
        id: 'role-1',
        name: 'Original Role',
        path: 'Original Role',
        parentId: null,
      };

      RoleRepository.findById.mockResolvedValue(mockRole);
      RoleValidation.validateRoleName.mockResolvedValue(true);
      RoleValidation.validateHierarchy.mockReturnValue(true);
      RoleValidation.validateAndFilterModulePolicies.mockResolvedValue([
        { moduleId: 'module-1', policies: ['canView', 'canWrite'] },
      ]);

      RoleRepository.update.mockResolvedValue({
        id: 'role-1',
        name: 'Updated Role',
        path: 'Updated Role',
        toJSON: () => ({
          id: 'role-1',
          name: 'Updated Role',
          path: 'Updated Role',
        }),
      });

      RoleRepository.findDescendantsByParentPath.mockResolvedValue([]);
      RoleRepository.findAllByParentId.mockResolvedValue([]);

      // Mock existing policies where policy.id doesn't match roleModule.id
      RoleModuleRepository.findAllByRoleIdWithPolicy.mockResolvedValue([
        {
          id: 'role-module-1',
          moduleId: 'module-1',
          policy: { id: 'different-policy-id' }, // This won't match in the find operation
        },
      ]);

      const mockRoleModule = {
        id: 'role-module-1',
        moduleId: 'module-1',
        toJSON: () => ({ id: 'role-module-1', moduleId: 'module-1' }),
      };

      RoleModuleRepository.bulkUpsert.mockResolvedValue([mockRoleModule]);

      PolicyRepository.bulkUpsert.mockResolvedValue([
        {
          id: 'policy-1',
          parentId: 'role-module-1',
          toJSON: () => ({ id: 'policy-1', parentId: 'role-module-1' }),
        },
      ]);

      const result = await update(mockRequest);

      expect(PolicyRepository.bulkUpsert).toHaveBeenCalledWith(
        mockServer,
        [
          {
            parentId: 'role-module-1',
            canView: true,
            canWrite: true,
            canEdit: false,
            updatedBy: 'user-1',
          },
        ],
        ['parentId'],
        { transaction: mockTransaction },
      );

      expect(result.result).toEqual(
        expect.objectContaining({
          id: 'role-1',
          name: 'Updated Role',
          path: 'Updated Role',
        }),
      );
    });
  });

  describe('updateStatus', () => {
    beforeEach(() => {
      mockRequest.body = { status: 'inactive' };

      const mockRole = {
        id: 'role-1',
        name: 'Test Role',
        status: 'active',
      };

      RoleRepository.findById.mockResolvedValue(mockRole);
      RoleRepository.update.mockResolvedValue({
        id: 'role-1',
        name: 'Test Role',
        status: 'inactive',
        toJSON: () => ({
          id: 'role-1',
          name: 'Test Role',
          status: 'inactive',
        }),
      });
    });

    it('should update the status of a role', async () => {
      const result = await updateStatus(mockRequest);

      expect(RoleRepository.findById).toHaveBeenCalledWith(mockServer, 'role-1', 'entity-1');
      expect(RoleRepository.update).toHaveBeenCalledWith(
        expect.objectContaining({ id: 'role-1' }),
        { status: 'inactive' },
        mockRequest.user,
      );

      expect(clearCacheWithPrefix).toHaveBeenCalled();
      expect(result).toEqual({
        result: expect.objectContaining({
          id: 'role-1',
          name: 'Test Role',
          status: 'inactive',
        }),
        auditModelMapping: expect.any(Object),
      });
    });

    it('should throw an error if role is not found', async () => {
      RoleRepository.findById.mockResolvedValue(null);
      CoreError.dataNotFound = vi.fn().mockReturnValue(new Error('Role not found'));

      await expect(updateStatus(mockRequest)).rejects.toThrow('Role not found');
      expect(CoreError.dataNotFound).toHaveBeenCalledWith({
        data: 'common.label.role',
        attribute: 'ID',
        value: 'role-1',
      });
    });
  });

  describe('options', () => {
    it('should return module policy options', async () => {
      const mockOptions = [
        { id: 'option-1', name: 'Option 1' },
        { id: 'option-2', name: 'Option 2' },
      ];

      getModulePolicyOptions.mockResolvedValue(mockOptions);

      const result = await options(mockRequest);

      expect(getModulePolicyOptions).toHaveBeenCalledWith(mockRequest);
      expect(result).toEqual(mockOptions);
    });
  });

  describe('navigations', () => {
    beforeEach(() => {
      mockRequest = {
        server: mockServer,
        entity: { id: 'entity-1' },
        authInfo: { roleId: 'role-123' },
      };

      RoleRepository.findById.mockResolvedValue({
        id: 'role-123',
        name: 'Test Role',
      });

      ModuleRepository.findAll.mockResolvedValue([
        {
          id: 'module-1',
          translationKey: 'Dashboard',
          navigationUrl: '/dashboard',
          parentId: null,
          hierarchy: 'root',
          navigationType: 'SIDE',
        },
        {
          id: 'module-2',
          translationKey: 'Users',
          navigationUrl: '/users',
          parentId: null,
          hierarchy: 'root',
          navigationType: 'SIDE',
        },
        {
          id: 'module-3',
          translationKey: 'User List',
          navigationUrl: '/userlist',
          parentId: 'module-2',
          hierarchy: 'root',
          navigationType: 'SIDE',
        },
        {
          id: 'module-4',
          name: 'Settings',
          navigationUrl: '/settings',
          parentId: null,
          hierarchy: 'root',
          navigationType: 'TOP',
        },
        {
          id: 'module-5',
          name: 'Reports',
          navigationUrl: '/reports',
          parentId: null,
          hierarchy: 'merchant',
          navigationType: 'SIDE',
        },
      ]);

      RoleModuleRepository.findAllByRoleIdWithPolicyAndModules.mockResolvedValue([
        {
          moduleId: 'module-1',
          policy: { canView: true, canEdit: true },
        },
        {
          moduleId: 'module-2',
          policy: { canView: true, canEdit: false },
        },
        {
          moduleId: 'module-3',
          policy: { canView: true, canEdit: false },
        },
        {
          moduleId: 'module-4',
          policy: { canView: true, canEdit: true },
        },
        {
          moduleId: 'module-5',
          policy: { canView: false, canEdit: false },
        },
      ]);
    });

    it('should return menu structure based on role permissions', async () => {
      const result = await navigations(mockRequest);

      expect(RoleRepository.findById).toHaveBeenCalledWith(
        mockServer,
        'role-123',
        'entity-1',
        true,
      );

      expect(RoleModuleRepository.findAllByRoleIdWithPolicyAndModules).toHaveBeenCalledWith(
        mockServer,
        'role-123',
      );

      expect(result).toHaveProperty('root');
      expect(result).toHaveProperty('merchant');

      expect(result.root).toHaveProperty('side');
      expect(result.root).toHaveProperty('top');

      expect(result.root.side).toContainEqual(
        expect.objectContaining({ name: 'Dashboard', url: '/dashboard' }),
      );

      expect(result.root.side).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            name: 'Dashboard',
            url: '/dashboard',
          }),
          {
            Users: [
              expect.objectContaining({
                name: 'User List',
                url: '/userlist',
              }),
            ],
          },
        ]),
      );

      expect(result.root.side).toContainEqual({
        Users: [
          expect.objectContaining({
            name: 'User List',
            url: '/userlist',
          }),
        ],
      });
    });

    it('should throw an error if user role is not found', async () => {
      RoleRepository.findById.mockResolvedValue(null);

      await expect(navigations(mockRequest)).rejects.toThrow();
    });

    it('should handle empty module policies', async () => {
      RoleModuleRepository.findAllByRoleIdWithPolicyAndModules.mockResolvedValue([]);

      const result = await navigations(mockRequest);

      expect(result.root.side).toEqual([]);
      expect(result.root.top).toEqual([]);
      expect(result.merchant.side).toEqual([]);
      expect(result.merchant.top).toEqual([]);
    });

    it('should handle modules with mixed navigation types', async () => {
      ModuleRepository.findAll.mockResolvedValue([
        {
          id: 'module-1',
          translationKey: 'Dashboard',
          navigationUrl: '/dashboard',
          parentId: null,
          hierarchy: 'root',
          navigationType: ['TOP', 'SIDE'],
        },
        {
          id: 'module-2',
          translationKey: 'Users',
          navigationUrl: '/users',
          parentId: null,
          hierarchy: 'root',
          navigationType: 'SIDE',
        },
      ]);

      RoleModuleRepository.findAllByRoleIdWithPolicyAndModules.mockResolvedValue([
        {
          moduleId: 'module-1',
          policy: { canView: true, canEdit: true },
        },
        {
          moduleId: 'module-2',
          policy: { canView: true, canEdit: false },
        },
      ]);

      const result = await navigations(mockRequest);

      expect(result.root.side).toContainEqual(
        expect.objectContaining({
          name: 'Dashboard',
          url: '/dashboard',
        }),
      );
      expect(result.root.top).toContainEqual(
        expect.objectContaining({
          name: 'Dashboard',
          url: '/dashboard',
        }),
      );

      expect(result.root.side).toContainEqual(
        expect.objectContaining({
          name: 'Users',
          url: '/users',
        }),
      );
      expect(result.root.top).not.toContainEqual(
        expect.objectContaining({
          name: 'Users',
          url: '/users',
        }),
      );
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle database transaction rollback on error', async () => {
      mockRequest.body = {
        departmentId: 'dept-1',
        name: 'Test Role',
        modules: [],
      };

      RoleRepository.create.mockRejectedValue(new Error('Database error'));
      mockServer.psql.connection.transaction.mockImplementation(async (callback) => {
        try {
          await callback(mockTransaction);
        } catch (error) {
          await mockTransaction.rollback();
          throw error;
        }
      });

      await expect(create(mockRequest)).rejects.toThrow('Database error');
    });
  });

  describe('navigations - computeSubtreeProperties', () => {
    beforeEach(() => {
      mockRequest = {
        server: mockServer,
        entity: { id: 'entity-1' },
        authInfo: { roleId: 'role-123' },
      };

      RoleRepository.findById.mockResolvedValue({
        id: 'role-123',
        name: 'Test Role',
      });
    });

    it('should set hasViewInSubtree to true when at least one child has view permission', async () => {
      ModuleRepository.findAll.mockResolvedValue([
        {
          id: 'parent-1',
          translationKey: 'Parent Module',
          navigationUrl: null,
          parentId: null,
          hierarchy: 'root',
          navigationType: 'SIDE',
        },
        {
          id: 'child-1',
          translationKey: 'Child 1',
          navigationUrl: '/child1',
          parentId: 'parent-1',
          hierarchy: 'root',
          navigationType: 'SIDE',
        },
        {
          id: 'child-2',
          translationKey: 'Child 2',
          navigationUrl: '/child2',
          parentId: 'parent-1',
          hierarchy: 'root',
          navigationType: 'SIDE',
        },
      ]);

      RoleModuleRepository.findAllByRoleIdWithPolicyAndModules.mockResolvedValue([
        {
          moduleId: 'parent-1',
          policy: { canView: false },
        },
        {
          moduleId: 'child-1',
          policy: { canView: true },
        },
        {
          moduleId: 'child-2',
          policy: { canView: false },
        },
      ]);

      const result = await navigations(mockRequest);

      expect(result.root.side).toContainEqual({
        'Parent Module': [
          expect.objectContaining({
            name: 'Child 1',
            url: '/child1',
          }),
        ],
      });
    });

    it('should set hasViewInSubtree to false when no children have view permission', async () => {
      ModuleRepository.findAll.mockResolvedValue([
        {
          id: 'parent-1',
          translationKey: 'Parent Module',
          navigationUrl: null,
          parentId: null,
          hierarchy: 'root',
          navigationType: 'SIDE',
        },
        {
          id: 'child-1',
          translationKey: 'Child 1',
          navigationUrl: '/child1',
          parentId: 'parent-1',
          hierarchy: 'root',
          navigationType: 'SIDE',
        },
        {
          id: 'child-2',
          translationKey: 'Child 2',
          navigationUrl: '/child2',
          parentId: 'parent-1',
          hierarchy: 'root',
          navigationType: 'SIDE',
        },
      ]);

      RoleModuleRepository.findAllByRoleIdWithPolicyAndModules.mockResolvedValue([
        {
          moduleId: 'parent-1',
          policy: { canView: false },
        },
        {
          moduleId: 'child-1',
          policy: { canView: false },
        },
        {
          moduleId: 'child-2',
          policy: { canView: false },
        },
      ]);

      const result = await navigations(mockRequest);

      expect(result.root.side).not.toContainEqual(
        expect.objectContaining({
          'Parent Module': expect.anything(),
        }),
      );
    });

    it('should handle nested parent-child relationships correctly', async () => {
      ModuleRepository.findAll.mockResolvedValue([
        {
          id: 'grandparent-1',
          translationKey: 'Grandparent',
          navigationUrl: null, // Non-leaf
          parentId: null,
          hierarchy: 'root',
          navigationType: 'SIDE',
        },
        {
          id: 'parent-1',
          translationKey: 'Parent',
          navigationUrl: null, // Non-leaf
          parentId: 'grandparent-1',
          hierarchy: 'root',
          navigationType: 'SIDE',
        },
        {
          id: 'child-1',
          translationKey: 'Child',
          navigationUrl: '/child',
          parentId: 'parent-1',
          hierarchy: 'root',
          navigationType: 'SIDE',
        },
      ]);

      RoleModuleRepository.findAllByRoleIdWithPolicyAndModules.mockResolvedValue([
        {
          moduleId: 'grandparent-1',
          policy: { canView: false },
        },
        {
          moduleId: 'parent-1',
          policy: { canView: false },
        },
        {
          moduleId: 'child-1',
          policy: { canView: true },
        },
      ]);

      const result = await navigations(mockRequest);

      expect(result.root.side).toContainEqual({
        Grandparent: [
          {
            Parent: [
              expect.objectContaining({
                name: 'Child',
                url: '/child',
              }),
            ],
          },
        ],
      });
    });

    it('should handle parent with no children gracefully', async () => {
      ModuleRepository.findAll.mockResolvedValue([
        {
          id: 'parent-1',
          translationKey: 'Parent Module',
          navigationUrl: null,
          parentId: null,
          hierarchy: 'root',
          navigationType: 'SIDE',
        },
      ]);

      RoleModuleRepository.findAllByRoleIdWithPolicyAndModules.mockResolvedValue([
        {
          moduleId: 'parent-1',
          policy: { canView: true },
        },
      ]);

      const result = await navigations(mockRequest);

      expect(result.root.side).toEqual([]);
    });
  });
});
