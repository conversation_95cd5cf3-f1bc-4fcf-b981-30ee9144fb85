import { COMMON_STATUSES, MODULE_NAMES } from '#src/modules/core/constants/core.constant.js';
import {
  OFFSET_PAGINATION_QUERY_PARAMS,
  createOffsetPaginationResponseSchema,
} from '#src/modules/core/schemas/core.schema.js';
import { CoreSchema } from '#src/modules/core/schemas/index.js';

const {
  COMMON_PROPERTIES,
  ERROR_RESPONSE,
  REQ_PARAM_UUID,
  CREATE_RESPONSE,
  UPDATE_RESPONSE,
  VIEW_RESPONSE,
} = CoreSchema;
const { ROLE } = MODULE_NAMES;

const TAGS = ['BO / User Management / Access Control / Roles'];

/**
 * Schema for policy settings object
 */
const POLICY_SCHEMA = {
  type: 'object',
  properties: {
    canView: { type: 'boolean' },
    canCreate: { type: 'boolean' },
    canEdit: { type: 'boolean' },
    canImport: { type: 'boolean' },
    canExport: { type: 'boolean' },
    canManage: { type: 'boolean' },
    canMasking: { type: 'boolean' },
    canOverwrite: { type: 'boolean' },
    canVerify: { type: 'boolean' },
  },
};

/**
 * Schema for module with policy settings
 */
const MODULE_WITH_POLICY = {
  type: 'object',
  properties: {
    id: { type: 'string', format: 'uuid' },
    name: { type: 'string' },
    policies: POLICY_SCHEMA,
  },
};

/**
 * Schema for module policies object
 */
const MODULE_POLICIES_SCHEMA = {
  type: 'object',
  properties: {
    root: {
      type: 'array',
      items: MODULE_WITH_POLICY,
    },
    organisation: {
      type: 'array',
      items: MODULE_WITH_POLICY,
    },
    merchant: {
      type: 'array',
      items: MODULE_WITH_POLICY,
    },
  },
};

/**
 * Response schema properties for role.
 */
const ROLE_RES_PROPERTIES = {
  ...COMMON_PROPERTIES,
  name: { type: 'string' },
  description: { type: 'string' },
  status: { type: 'string', enum: Object.values(COMMON_STATUSES) },
  parentName: { type: 'string' },
  modules: MODULE_POLICIES_SCHEMA,
  metadata: { type: 'object', additionalProperties: true },
  version: { type: 'integer' },
};

/**
 * Schema for module in request body
 */
const MODULE_REQUEST_SCHEMA = {
  type: 'object',
  properties: {
    moduleId: { type: 'string', format: 'uuid' },
    policies: {
      type: 'array',
      items: { type: 'string' },
    },
  },
  required: ['moduleId', 'policies'],
};

/**
 * Schema for module in options response
 */
const MODULE_OPTIONS_SCHEMA = {
  type: 'object',
  properties: {
    id: { type: 'string', format: 'uuid' },
    name: { type: 'string' },
    hierarchy: { type: 'string' },
    navigationPosition: { type: 'integer' },
    navigationType: { type: 'string' },
    translationKey: { type: 'string' },
    level: { type: 'integer' },
    parentId: { type: 'string', format: 'uuid' },
    navigationUrl: { type: 'string' },
    policy: POLICY_SCHEMA,
    children: {
      type: 'array',
      items: {
        type: 'object',
        additionalProperties: true,
      },
    },
  },
};

/**
 * Recursive schema for menu items that can be either strings or objects with nested items
 */
const MENU_ITEM = {
  oneOf: [
    { type: 'string' },
    {
      type: 'object',
      additionalProperties: {
        type: 'array',
        items: {
          oneOf: [
            { type: 'string' },
            {
              type: 'object',
              properties: {
                name: { type: 'string' },
                url: { type: 'string' },
              },
              required: ['name', 'url'],
            },
            {
              type: 'object',
              additionalProperties: {
                type: 'array',
                items: {
                  oneOf: [
                    { type: 'string' },
                    {
                      type: 'object',
                      properties: {
                        name: { type: 'string' },
                        url: { type: 'string' },
                      },
                      required: ['name', 'url'],
                    },
                  ],
                },
              },
            },
          ],
        },
      },
    },
  ],
};

/**
 * Schema for navigation menu structure that supports mixed content at any level
 */
const NAVIGATION_MENU_SCHEMA = {
  type: 'array',
  items: MENU_ITEM,
};

/**
 * List endpoint schema for role.
 */
export const index = {
  tags: TAGS,
  summary: `Get a list of ${ROLE}`,
  querystring: {
    type: 'object',
    properties: {
      filter_name_eq: { type: 'string' },
      filter_parentId_eq: { type: 'string' },
      filter_departmentId_eq: { type: 'string' },
      filter_status_eq: {
        oneOf: [
          {
            type: 'array',
            items: { type: 'string' },
            uniqueItems: true,
            minItems: 1,
          },
          { type: 'string', enum: Object.values(COMMON_STATUSES) },
        ],
      },
      ...OFFSET_PAGINATION_QUERY_PARAMS,
      sortBy: {
        oneOf: [{ type: 'string' }, { type: 'array' }],
        description: 'Sorting field in the format (field:order)',
      },
    },
  },
  response: {
    200: {
      description: 'Success response',
      type: 'object',
      properties: {
        message: { type: 'string', description: 'Success message' },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: ROLE_RES_PROPERTIES,
          },
        },
        meta: createOffsetPaginationResponseSchema(),
      },
    },
    ...ERROR_RESPONSE,
  },
};

/**
 * View endpoint schema for role.
 */
export const view = {
  tags: TAGS,
  summary: `View a ${ROLE}`,
  params: REQ_PARAM_UUID,
  response: VIEW_RESPONSE({
    type: 'object',
    properties: ROLE_RES_PROPERTIES,
  }),
};

/**
 * Create role schema.
 */
export const create = {
  tags: TAGS,
  summary: `Create a ${ROLE}`,
  body: {
    type: 'object',
    properties: {
      name: { type: 'string' },
      description: { type: 'string' },
      parentId: { type: 'string', format: 'uuid' },
      departmentId: { type: 'string', format: 'uuid' },
      modules: {
        type: 'array',
        items: MODULE_REQUEST_SCHEMA,
      },
    },
    required: ['departmentId', 'modules', 'name'],
  },
  response: CREATE_RESPONSE,
};

/**
 * Update role schema.
 */
export const update = {
  tags: TAGS,
  summary: `Update a ${ROLE}`,
  params: REQ_PARAM_UUID,
  body: {
    type: 'object',
    properties: {
      name: { type: 'string' },
      description: { type: 'string' },
      modules: {
        type: 'array',
        items: MODULE_REQUEST_SCHEMA,
      },
      version: { type: 'number', default: 1 },
    },
    required: ['name', 'modules', 'version'],
  },
  response: UPDATE_RESPONSE,
};

/**
 * Update status schema for role.
 */
export const updateStatus = {
  tags: TAGS,
  summary: `Update a ${ROLE} status`,
  params: REQ_PARAM_UUID,
  body: {
    type: 'object',
    properties: {
      status: { type: 'string', enum: Object.values(COMMON_STATUSES) },
      version: { type: 'number' },
    },
    required: ['version', 'status'],
  },
  response: UPDATE_RESPONSE,
};

export const options = {
  tags: TAGS,
  summary: 'Get available role options',
  querystring: {
    type: 'object',
    properties: {
      filter_parentId_eq: { type: 'string' },
      filter_departmentId_eq: { type: 'string' },
    },
  },
  response: {
    200: {
      description: 'Success response',
      type: 'object',
      properties: {
        message: { type: 'string' },
        data: {
          type: 'object',
          properties: {
            root: {
              type: 'array',
              items: MODULE_OPTIONS_SCHEMA,
            },
            organisation: {
              type: 'array',
              items: MODULE_OPTIONS_SCHEMA,
            },
            merchant: {
              type: 'array',
              items: MODULE_OPTIONS_SCHEMA,
            },
          },
        },
        debug: {
          type: 'object',
          properties: {
            appName: { type: 'string' },
            environment: { type: 'string' },
            requestId: { type: 'string' },
            version: { type: 'string' },
          },
        },
      },
    },
    ...ERROR_RESPONSE,
  },
};

/**
 * Navigation endpoint schema
 */
export const navigations = {
  tags: TAGS,
  summary: 'Get user navigation menus based on role policies',
  response: {
    200: {
      description: 'Success response',
      type: 'object',
      properties: {
        message: { type: 'string' },
        data: {
          type: 'object',
          properties: {
            root: {
              type: 'object',
              properties: {
                side: NAVIGATION_MENU_SCHEMA,
                top: NAVIGATION_MENU_SCHEMA,
                personal: NAVIGATION_MENU_SCHEMA,
              },
            },
            organisation: {
              type: 'object',
              properties: {
                side: NAVIGATION_MENU_SCHEMA,
                top: NAVIGATION_MENU_SCHEMA,
                personal: NAVIGATION_MENU_SCHEMA,
              },
            },
            merchant: {
              type: 'object',
              properties: {
                side: NAVIGATION_MENU_SCHEMA,
                top: NAVIGATION_MENU_SCHEMA,
                personal: NAVIGATION_MENU_SCHEMA,
              },
            },
            user: {
              type: 'object',
              properties: {
                side: NAVIGATION_MENU_SCHEMA,
                top: NAVIGATION_MENU_SCHEMA,
                personal: NAVIGATION_MENU_SCHEMA,
              },
            },
          },
        },
        debug: {
          type: 'object',
          properties: {
            appName: { type: 'string' },
            environment: { type: 'string' },
            requestId: { type: 'string' },
            version: { type: 'string' },
          },
        },
      },
    },
    ...ERROR_RESPONSE,
  },
};
