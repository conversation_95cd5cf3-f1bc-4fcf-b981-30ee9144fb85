import { CoreError } from '#src/modules/core/errors/index.js';
import { ModuleRepository } from '#src/modules/core/repository/index.js';
import { RoleConstant } from '#src/modules/user/constants/index.js';
import { RoleError } from '#src/modules/user/errors/index.js';
import {
  DepartmentRepository,
  PolicyRepository,
  RoleModuleRepository,
  RoleRepository,
} from '#src/modules/user/repository/index.js';

const { POLICIES, ALLOWED_HIERARCHIES } = RoleConstant;
/**
 * Validates role name uniqueness.
 * @param {Object} server - The server object.
 * @param {string} entityId - The entity ID.
 * @param {string} name - The role name.
 * @param {string|null} excludeId - The role ID to exclude.
 * @throws {Error} If name is not unique.
 */
export const validateRoleName = async (server, entityId, name, excludeId = null) => {
  const existingRole = await RoleRepository.findByEntityIdAndName(
    server,
    entityId,
    name,
    excludeId,
  );

  if (existingRole) {
    throw CoreError.alreadyExists({ attribute: 'common.label.role', value: name });
  }
};

/**
 * Validates and filters module policies.
 * @param {Object} server - The server object.
 * @param {Array} modules - Array of modules with policies.
 * @param {string} hierarchy - The user hierarchy.
 * @param {string|null} parentId - The parent role ID.
 * @param {string|null} departmentId - The department ID.
 * @returns {Promise<Array>} Filtered module policies.
 * @throws {Error} If validation fails.
 */
export const validateAndFilterModulePolicies = async (
  server,
  modules,
  hierarchy,
  parentId = null,
  departmentId = null,
) => {
  const uniqueModules = new Map();
  const moduleIds = [];

  for (const module of modules) {
    const moduleId = module.moduleId;
    if (!uniqueModules.has(moduleId)) {
      uniqueModules.set(moduleId, {
        moduleId,
        policies: module.policies || [],
      });
      moduleIds.push(moduleId);
    }
  }

  const [moduleInfoMap, supportedPoliciesMap, departmentModuleIds, parentPolicies] =
    await Promise.all([
      ModuleRepository.findByIds(server, moduleIds).then(
        (rows) => new Map(rows.map((m) => [m.id, m])),
      ),

      PolicyRepository.findByParentIds(server, moduleIds).then((rows) =>
        processPolicyRows(
          rows,
          (_, data) => data.parentId,
          (row) => row.toJSON?.() ?? row.dataValues ?? row,
        ),
      ),

      departmentId
        ? DepartmentRepository.findAllModulePolicies(server, departmentId).then(
            (rows) => new Set(rows.map((m) => m.moduleId)),
          )
        : Promise.resolve(null),

      parentId
        ? RoleModuleRepository.findAllByRoleIdWithPolicy(server, parentId).then((rows) =>
            processPolicyRows(
              rows,
              (row) => row.moduleId,
              (row) => row.policy.toJSON(),
            ),
          )
        : Promise.resolve(new Map()),
    ]);

  validateModuleExistence(moduleInfoMap, moduleIds);
  validateDepartmentModules(departmentId, departmentModuleIds, moduleIds);

  const results = [];
  for (const { moduleId, policies: requestedPolicies } of uniqueModules.values()) {
    const moduleInfo = moduleInfoMap.get(moduleId);

    validatePoliciesForHierarchy(moduleInfo.hierarchy, hierarchy, moduleId);
    validateSupportedPolicies(moduleId, requestedPolicies, supportedPoliciesMap, moduleInfo);

    if (parentId) {
      validateParentPolicies(moduleId, requestedPolicies, parentPolicies, moduleInfo);
    }

    results.push({ moduleId, policies: requestedPolicies });
  }

  return results;
};

/**
 * Validates that all modules in the moduleIds array exist in the moduleInfoMap.
 * @param {Map<string, Object>} moduleInfoMap - Map of module IDs to module information
 * @param {Array<string>} moduleIds - Array of module IDs to validate
 * @returns {void}
 * @throws {CoreError.dataNotFound} If any modules are missing
 */
const validateModuleExistence = (moduleInfoMap, moduleIds) => {
  if (moduleInfoMap.size !== moduleIds.length) {
    const missingModuleIds = moduleIds.filter((id) => !moduleInfoMap.has(id));
    if (missingModuleIds.length > 0) {
      throw CoreError.dataNotFound({
        data: 'common.label.module',
        attribute: 'ID',
        value: missingModuleIds.join(', '),
      });
    }
  }
};

/**
 * Validates that all modules in the moduleIds array are part of the department's module set.
 * @param {string} departmentId - The ID of the department to validate against
 * @param {Set<string>} departmentModuleIds - Set of module IDs that are part of the department
 * @param {Array<string>} moduleIds - Array of module IDs to validate
 * @returns {void}
 * @throws {RoleError.moduleNotInDepartment} If any modules are not in the department's module set
 */
const validateDepartmentModules = (departmentId, departmentModuleIds, moduleIds) => {
  if (departmentId && departmentModuleIds) {
    for (const moduleId of moduleIds) {
      if (!departmentModuleIds.has(moduleId)) {
        throw RoleError.moduleNotInDepartment({
          moduleId,
          departmentId,
        });
      }
    }
  }
};

/**
 * Validates policies for a given hierarchy.
 * @param {Array} policies - The policies to validate.
 * @param {string} moduleHierarchy - The module hierarchy.
 * @param {string} hierarchy - The user hierarchy.
 * @param {string} moduleId - The module ID.
 * @returns {Array} The validated policies.
 * @throws {Error} If the hierarchy is invalid for the module.
 */
export const validatePoliciesForHierarchy = (moduleHierarchy, hierarchy, moduleId) => {
  if (!ALLOWED_HIERARCHIES[hierarchy].includes(moduleHierarchy)) {
    throw RoleError.invalidHierarchy({
      id: moduleId,
    });
  }
};

/**
 * Validates that the requested policies are supported by the module.
 * Throws an error if any unsupported policies are found.
 *
 * @param {string} moduleId - The ID of the module to validate policies for
 * @param {Array<string>} requestedPolicies - The list of policies to validate
 * @param {Map<string, Set<string>>} supportedPoliciesMap - Map of module IDs to sets of supported policies
 * @param {Object} moduleInfo - Information about the module
 * @throws {Error} If any policies are not supported by the module
 */
export const validateSupportedPolicies = (
  moduleId,
  requestedPolicies,
  supportedPoliciesMap,
  moduleInfo,
) => {
  const supportedSet = supportedPoliciesMap.get(moduleId) ?? new Set();
  const unsupported = requestedPolicies.filter((p) => !supportedSet.has(p));
  if (unsupported.length > 0) {
    throw RoleError.unsupportedModulePolicies({
      module: moduleInfo.translationKey,
      policies: unsupported,
    });
  }
};

/**
 * Validates that the requested policies don't exceed those of the parent role.
 * Throws an error if the module is not accessible by the parent role or if
 * any policies exceed the parent's permissions.
 *
 * @param {string} moduleId - The ID of the module to validate policies for
 * @param {Array<string>} requestedPolicies - The list of policies to validate
 * @param {Map<string, Object>} parentPolicies - Map of module IDs to parent policy objects
 * @param {Object} moduleInfo - Information about the module
 * @throws {Error} If the module is not accessible by the parent role
 * @throws {Error} If any policies exceed the parent's permissions
 */
export const validateParentPolicies = (moduleId, requestedPolicies, parentPolicies, moduleInfo) => {
  const parentModulePolicies = parentPolicies.get(moduleId);
  if (!parentModulePolicies) {
    throw RoleError.parentHasNoPoliciesAccess({ module: moduleInfo.translationKey });
  }
  const exceeding = requestedPolicies.filter((p) => !parentModulePolicies.has(p));
  if (exceeding.length > 0) {
    throw RoleError.exceedsParentPolicies();
  }
};

/**
 * Checks if a role is within the user's hierarchy policies.
 * @param {string} userPath - The user's role path.
 * @param {string} rolePath - The role path to check.
 * @param {string} userHierarchy - The user's hierarchy level (root, organisation, merchant).
 * @param {string} roleHierarchy - The role's hierarchy level (root, organisation, merchant).
 * @returns {boolean} True if the user is authorised to update the role.
 */
export const validateHierarchy = (userPath, rolePath, userHierarchy, roleHierarchy) => {
  switch (userHierarchy) {
    case 'root':
      return roleHierarchy === 'root' ? rolePath.startsWith(userPath) : true;
    case 'organisation':
      switch (roleHierarchy) {
        case 'root':
          return false;
        case 'organisation':
          return rolePath.startsWith(userPath);
        case 'merchant':
          return true;
        default:
          return false;
      }

    case 'merchant':
      return roleHierarchy === 'merchant' && rolePath.startsWith(userPath);

    default:
      return false;
  }
};

/**
 * Extracts enabled policies from a data object.
 * @param {Object} data - The data object containing policy settings.
 * @returns {Set<string>} A set of enabled policy names.
 */
const extractEnabledPolicies = (data) => {
  const enabledPolicies = new Set();
  for (const [key, value] of Object.entries(data)) {
    if (POLICIES.includes(key) && value === true) {
      enabledPolicies.add(key);
    }
  }
  return enabledPolicies;
};

/**
 * Processes policy rows and creates a map of keys to policy sets.
 * @param {Array} rows - The rows to process.
 * @param {Function} getKeyFn - Function to extract the key from a row.
 * @param {Function} getDataFn - Function to extract the data from a row.
 * @returns {Map<string, Set<string>>} A map of keys to sets of policy names.
 */
const processPolicyRows = (rows, getKeyFn, getDataFn) => {
  const map = new Map();

  for (const row of rows) {
    const data = getDataFn(row);
    const key = getKeyFn(row, data);
    const policies = extractEnabledPolicies(data);

    if (policies.size > 0) {
      const existingPolicies = map.get(key);
      if (existingPolicies) {
        for (const policy of policies) {
          existingPolicies.add(policy);
        }
      } else {
        map.set(key, policies);
      }
    }
  }

  return map;
};
